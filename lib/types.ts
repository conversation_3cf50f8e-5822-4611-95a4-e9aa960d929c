import type { Database } from './supabase/database.types'; // Assuming this path is correct relative to types.ts

// Base row types from auto-generated Supabase types
export type ProjectRow = Database['public']['Tables']['projects']['Row'];
export type ChapterRow = Database['public']['Tables']['chapters']['Row'];

// Application-specific type alias for Project, directly using the Row type
// Any additional client-side properties can be added by extending this if necessary,
// but for data fetched from DB, this should be the shape.
export interface Book extends ProjectRow {
  // Example of adding a purely client-side optional field:
  // author_nickname?: string;
}

// Application-specific type alias for Chapter
export interface Chapter extends ChapterRow {
  // Example of adding a purely client-side optional field:
  // is_currently_viewing?: boolean;
}

export interface BookProgress {
  // We're currently calculating this from chapters rather than storing it directly
  calculated_word_count: number; 
  goal: number;
  progress_percentage: number;
}

// Magic Edit™ related types
export interface MagicEdit {
  id: string;
  user_id: string;
  project_id: string;
  chapter_id: string;
  word_count: number;
  tokens_used: number;
  created_at: string;
}

export interface UserPlan {
  magic_edits_used: number;
  magic_edits_limit: number;
  bookmemory_enabled: boolean;
}

// Track edited sections in a chapter
export interface EditedSection {
  start_index: number;
  end_index: number;
  status: 'edited' | 'accepted' | 'pending';
  timestamp: string;
}

export interface ChapterEditProgress {
  chapter_id: string;
  edited_sections: EditedSection[];
  last_updated: string;
}

// BookMemory™ related types
export interface BookMemory {
  id: string;
  project_id: string;
  chapter_id: string;
  summary_text: string;
  created_at: string;
}
