'use client';

import useS<PERSON> from 'swr';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useAuth } from '@/lib/providers/auth-provider';

// Define credit data interface
export interface UserCreditData {
  magicEditsUsed: number;
  magicEditLimit: number;
  magicEditsRemaining: number;
  isPaidUser: boolean;
  bookMemoryEnabled: boolean;
  plan: 'free' | 'paid';
}

// Default values for user credits
const DEFAULT_CREDITS: UserCreditData = {
  magicEditsUsed: 0,
  magicEditLimit: 5,
  magicEditsRemaining: 5,
  isPaidUser: false,
  bookMemoryEnabled: false,
  plan: 'free',
};

/**
 * Enhanced fetcher function for SWR that handles authentication and errors properly
 * @param url The API endpoint to fetch from
 * @returns Parsed JSON response
 * @throws Error with proper information for SWR to handle rate limiting
 */
const creditsFetcher = async (url: string): Promise<UserCreditData> => {
  try {
    // Import Supabase client
    const { supabase } = await import('@/lib/supabase/client');

    // Get the current session
    const { data: sessionData } = await supabase.auth.getSession();
    const session = sessionData?.session;

    // Try to get auth token from session
    let authToken = session?.access_token || null;

    // Fallback to localStorage if session doesn't have a token
    if (!authToken) {
      try {
        console.log('[creditsFetcher] No token in session, checking localStorage');
        const supabaseKey = Object.keys(localStorage).find(key => key.startsWith('sb-'));
        if (supabaseKey) {
          const supabaseData = JSON.parse(localStorage.getItem(supabaseKey) || '{}');
          if (supabaseData?.access_token) {
            authToken = supabaseData.access_token;
            console.log('[creditsFetcher] Got auth token from localStorage');
          }
        }
      } catch (e) {
        console.error('[creditsFetcher] Error accessing localStorage:', e);
      }
    } else {
      console.log('[creditsFetcher] Got auth token from session');
    }

    // Log auth status (without exposing the actual token)
    console.log('[creditsFetcher] Auth token available:', !!authToken);

    // Prepare request headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
    };

    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    // Add a small delay to avoid hitting rate limits on rapid component mounts
    // This is a "poor man's throttling" approach that helps prevent bursts
    const randomDelay = Math.random() * 100; // 0-100ms random delay
    await new Promise(resolve => setTimeout(resolve, randomDelay));

    // Make the request
    const response = await fetch(url, {
      credentials: 'include', // Important for cookie-based auth
      headers,
    });

    // Critical: Handle 429 rate limit errors properly by raising an error that SWR can handle
    if (response.status === 429) {
      // Create a plain error object first
      const error = new Error('Rate limit exceeded');

      // Then add properties in a type-safe way
      const customError = error as Error & {
        status?: number;
        info?: { headers?: Headers };
        data?: UserCreditData
      };

      customError.status = 429;
      customError.info = { headers: response.headers };

      // Try to get body data even when rate limited (our API returns cached data)
      try {
        const data = await response.json();

        // Return the data even though we're rate limited
        // This ensures UI doesn't break when rate limited
        console.log('[creditsFetcher] Got data despite rate limiting, using it');
        customError.data = {
          magicEditsUsed: data.magicEditsUsed ?? 0,
          magicEditLimit: data.magicEditLimit ?? 5,
          magicEditsRemaining: data.magicEditsRemaining ?? 5,
          isPaidUser: data.isPaidUser ?? false,
          bookMemoryEnabled: data.bookMemoryEnabled ?? false,
          plan: data.plan ?? 'free',
        };
      } catch (e) {
        console.error('[creditsFetcher] Could not parse rate-limited response:', e);
        // No valid data from rate limited response, will use default
      }

      throw customError;
    }

    // Handle other HTTP errors
    if (!response.ok) {
      const errorText = await response.text();

      // Create a plain error object first
      const error = new Error(`API error (${response.status}): ${errorText.replace(/"/g, '\\"')}`);

      // Then add properties in a type-safe way
      const customError = error as Error & {
        status?: number;
        info?: { headers?: Headers };
      };

      customError.status = response.status;
      customError.info = { headers: response.headers };

      throw customError;
    }

    // Parse rate limit headers (for client-side tracking)
    const rateLimit = {
      limit: response.headers.get('X-RateLimit-Limit'),
      remaining: response.headers.get('X-RateLimit-Remaining'),
      reset: response.headers.get('X-RateLimit-Reset')
    };

    // Log rate limit info for debugging
    if (rateLimit.remaining) {
      console.log(`[creditsFetcher] Rate limit: ${rateLimit.remaining}/${rateLimit.limit} remaining`);
    }

    const data = await response.json();

    // Ensure all required properties exist with proper defaults
    return {
      magicEditsUsed: data.magicEditsUsed ?? 0,
      magicEditLimit: data.magicEditLimit ?? 5,
      magicEditsRemaining: data.magicEditsRemaining ?? 5,
      isPaidUser: data.isPaidUser ?? false,
      bookMemoryEnabled: data.bookMemoryEnabled ?? false,
      plan: data.plan ?? 'free',
    };
  } catch (error: any) {
    console.error('[creditsFetcher] Error fetching credits:', error);

    // Define custom error interface to handle our extended error properties
    interface SWRError extends Error {
      status?: number;
      info?: {
        headers: Headers;
      };
      data?: UserCreditData;
    }

    // If the error has rate limit data, rethrow it for SWR to handle
    const swrError = error as SWRError;
    if (swrError.status === 429) {
      // If we have data from the rate-limited response, return it
      if (swrError.data) {
        return swrError.data;
      }
      // Otherwise, let SWR handle the retry
      throw swrError;
    }

    // Return default values for other errors
    return DEFAULT_CREDITS;
  }
};

/**
 * SWR hook for fetching and managing user credit data
 * - Implements caching and automatic revalidation
 * - Provides methods for manual revalidation
 * - Handles event-based updates
 */
export function useCredits(userId?: string) {
  const { getAuthToken } = useAuth();
  const [eventsInitialized, setEventsInitialized] = useState(false);

// Module-level variable to track the last refresh time globally
let globalLastRefreshTime = 0;
const GLOBAL_REFRESH_COOLDOWN = 5000; // 5 seconds

  // Enhanced SWR configuration
  const swrConfig = {
    // Increased deduplication interval to reduce API calls
    dedupingInterval: 10000,  // 10 seconds (increased from 5s)

    // Enhanced cache strategy
    // Don't revalidate on focus - prevents excessive requests when switching tabs/windows
    revalidateOnFocus: false,

    // Don't revalidate on reconnect - prevents unnecessary refreshes after network reconnection
    revalidateOnReconnect: false,

    // Keep stale data while revalidating to prevent UI flicker
    keepPreviousData: true,

    // Increase cache freshness period
    // Data will be considered fresh for 60 seconds (increased from 30s)
    // After that, it will still be used but revalidated in the background
    freshAge: 60000,  // 60 seconds

  // Custom error retry logic with improved rate limit handling
  onErrorRetry: (error: any, key: string, config: any, revalidate: any, { retryCount }: { retryCount: number }) => {
    // Define custom error interface to handle our extended error properties
    interface SWRError extends Error {
      status?: number;
      info?: {
        headers: Headers;
      };
    }

    // Type guard to check if error has status property
    function hasStatus(error: any): error is Error & { status: number } {
      return error && typeof error === 'object' && 'status' in error;
    }

    // Don't retry on auth issues - user needs to re-authenticate
    if (hasStatus(error) && (error.status === 401 || error.status === 403)) {
      console.log('[useCredits] Authentication error, not retrying');
      return;
    }

    // Use the error directly since we've already checked for status
    const swrError = error;

      // Enhanced rate limit handling
      if (swrError.status === 429) {
      // Extract headers for better rate limit handling
      const headers = swrError?.info?.headers;

      // Get Retry-After header (preferred method)
      const retryAfter = headers?.get('Retry-After');

        // Get X-RateLimit-Reset as fallback
        const rateLimitReset = headers?.get('X-RateLimit-Reset');

        // Calculate retry time with multiple fallbacks
        let retryDelaySeconds = 5; // Default fallback: 5 seconds

        if (retryAfter) {
          // Server explicitly told us when to retry
          retryDelaySeconds = parseInt(retryAfter, 10);
          console.log(`[useCredits] Using Retry-After header: ${retryDelaySeconds}s`);
        } else if (rateLimitReset) {
          // Calculate time until rate limit reset
          const resetTimestamp = parseInt(rateLimitReset, 10) * 1000;
          const now = Date.now();
          const timeUntilReset = Math.max(1, Math.ceil((resetTimestamp - now) / 1000));
          retryDelaySeconds = Math.min(timeUntilReset, 30); // Cap at 30 seconds
          console.log(`[useCredits] Using X-RateLimit-Reset: ${retryDelaySeconds}s`);
        }

        // Apply jitter to prevent thundering herd
        const jitter = Math.random() * 0.3 + 0.85; // Random value between 0.85 and 1.15
        const delayWithJitter = Math.ceil(retryDelaySeconds * jitter) * 1000;

        console.log(`[useCredits] Rate limited. Retrying after ${delayWithJitter/1000}s (with jitter)`);
        setTimeout(() => revalidate({ retryCount }), delayWithJitter);
        return;
      }

      // Only retry up to 3 times for other errors
      if (retryCount >= 3) {
        console.log('[useCredits] Max retries reached, giving up');
        return;
      }

      // Improved exponential backoff for other errors
      const baseDelay = 1000; // 1 second
      const exponentialDelay = baseDelay * Math.pow(1.5, retryCount); // 1.5ˣ growth
      const maxDelay = 15000; // Cap at 15 seconds
      const delay = Math.min(exponentialDelay, maxDelay);

      // Add jitter to prevent thundering herd
      const jitter = Math.random() * 0.3 + 0.85; // Random value between 0.85 and 1.15
      const delayWithJitter = Math.floor(delay * jitter);

      console.log(`[useCredits] Retrying after ${delayWithJitter}ms (attempt ${retryCount + 1}/3)`);
      setTimeout(() => revalidate({ retryCount }), delayWithJitter);
    },
  };

  // Conditional SWR fetching - only fetch if userId exists
  const {
    data = DEFAULT_CREDITS,
    error,
    isLoading,
    isValidating,
    mutate
  } = useSWR(
    // REMOVED time-based cache busting from SWR key to prevent excessive API calls.
    // Revalidation is handled by mutate() and event listeners.
    userId ? `/api/user-credits` : null,
    creditsFetcher,
    swrConfig
  );

  // Set up event listener for magic-edit-completed events with improved debouncing
  useEffect(() => {
    if (!userId || eventsInitialized) return;

    // Track the last time we handled a magic edit event to prevent multiple rapid refreshes
    let lastEventHandleTime = 0;
    const MIN_EVENT_INTERVAL = 5000; // 5 seconds between event-triggered refreshes (increased from 2s)

    const handleMagicEditCompleted = (event: Event) => {
      const now = Date.now();
      const customEvent = event as CustomEvent;

      // Check if we've handled an event recently
      if (now - lastEventHandleTime < MIN_EVENT_INTERVAL) {
        console.log('[useCredits] Ignoring rapid magic-edit-completed event (debounced)');
        return;
      }

      // Update last handle time
      lastEventHandleTime = now;

      // Get event details if available
      const timestamp = customEvent?.detail?.timestamp || now;
      console.log(`[useCredits] Handling magic-edit-completed event, timestamp: ${new Date(timestamp).toISOString()}`);

      // Wait a delay before revalidating to ensure server data is updated
      // Use a 1.5s delay to allow server to process the update
      setTimeout(() => {
        console.log('[useCredits] Refreshing credits after magic-edit-completed event');
        mutate();
      }, 1500);
    };

    // Add event listener
    window.addEventListener('magic-edit-completed', handleMagicEditCompleted);
    setEventsInitialized(true);

    // Clean up event listener on unmount or userId change
    return () => {
      window.removeEventListener('magic-edit-completed', handleMagicEditCompleted);
    };
  }, [userId, mutate, eventsInitialized]);

  // Function to manually refresh credits
  const refreshCredits = async (forceRefresh: boolean = false) => {
    try {
      const now = Date.now();

      // For force refresh, bypass rate limiting to ensure we get fresh data
      if (!forceRefresh && now - globalLastRefreshTime < GLOBAL_REFRESH_COOLDOWN) {
        console.log(`[useCredits] Ignoring rapid refresh attempt (GLOBAL rate limited). forceRefresh was: ${forceRefresh}`);
        return data; // Return current data if rate limited
      }

      // Update global last refresh time
      globalLastRefreshTime = now;

      console.log(`[useCredits] Proceeding with GLOBAL refresh. forceRefresh: ${forceRefresh}`);

      if (forceRefresh) {
        // For force refresh, completely invalidate the cache and fetch fresh data
        console.log('[useCredits] Force refresh: invalidating cache and fetching fresh data');

        // Force a complete cache invalidation and fresh fetch
        console.log('[useCredits] Invalidating cache completely');

        // First, clear the cache
        await mutate(undefined, false);

        // Then force a fresh fetch with cache busting using the correct parameter and headers
        const forceRefreshUrl = `/api/user-credits?refresh=true&t=${Date.now()}`;

        // Make a direct fetch call with cache-busting headers
        const response = await fetch(forceRefreshUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache', // This is what the API checks for
            'Pragma': 'no-cache',
            'Expires': '0'
          },
          cache: 'no-store'
        });

        if (!response.ok) {
          throw new Error(`Failed to force refresh credits: ${response.status} ${response.statusText}`);
        }

        const freshData = await response.json();

        // Update the cache with fresh data
        await mutate(freshData, false);

        console.log('[useCredits] Fresh data fetched and cached:', freshData);
        return freshData;
      } else {
        // Standard mutate, SWR will decide to revalidate based on its configuration
        const result = await mutate();
        console.log('[useCredits] Standard refresh completed');
        return result;
      }
    } catch (error) {
      console.error('[useCredits] Error refreshing credits:', error);
      // Don't rethrow to prevent UI disruption, SWR will handle error state
      throw error; // Actually, let's rethrow for force refresh so we can handle it properly
    }
  };
  // No need for instance-specific lastRefreshTime

  // Calculate progress percentage for UI
  const progressPercentage = Math.min(100, Math.round((data.magicEditsUsed / data.magicEditLimit) * 100));

  // Calculate whether the user is low on credits
  const isLowOnCredits = data.magicEditsRemaining > 0 && data.magicEditsRemaining <= 2;

  // Utility function to check if a user can use Magic Edit
  const canUseMagicEdit = () => {
    if (data.magicEditsRemaining <= 0) {
      if (data.isPaidUser) {
        toast.error(
          'You\'ve used all your Magic Edit™ credits for this month. Your credits will reset next month.',
          { duration: 5000 }
        );
      } else {
        toast.error(
          'You\'ve used all your free Magic Edit™ credits. Upgrade to Pro for more credits!',
          { duration: 5000 }
        );
      }
      return false;
    }

    // If less than 2 credits left, warn the user but still allow the operation
    if (isLowOnCredits) {
      toast(`You have ${data.magicEditsRemaining} Magic Edit™ credits remaining this month.`, {
        duration: 4000,
      });
    }

    return true;
  };

  // Function to safely trigger a magic edit event (with throttling)
  const notifyMagicEditCompleted = () => {
    try {
      // Import and use our event throttler for safer event dispatching
      const { safeDispatchEvent } = require('@/lib/event-throttler');
      return safeDispatchEvent('magic-edit-completed', { timestamp: Date.now() });
    } catch (e) {
      // Fallback to direct event dispatching if module not found
      console.warn('[useCredits] Could not use event-throttler, falling back to direct event');
      const event = new CustomEvent('magic-edit-completed', {
        detail: { timestamp: Date.now() }
      });
      window.dispatchEvent(event);
      return true;
    }
  };

  // Return enhanced hook data with more utilities
  return {
    // Core data
    credits: data,
    isLoading,
    isValidating,
    error,

    // Credit status helpers
    progressPercentage,
    isLowOnCredits,
    hasCreditsRemaining: data.magicEditsRemaining > 0,
    isPaidUser: data.isPaidUser,

    // Data refreshing functions
    refreshCredits,
    notifyMagicEditCompleted,

    // Credit usage helpers
    canUseMagicEdit,

    // Raw credit data for direct access
    magicEditsRemaining: data.magicEditsRemaining,
    magicEditLimit: data.magicEditLimit,
    magicEditsUsed: data.magicEditsUsed
  };
}
