/**
 * NUCLEAR EVENT THROTTLER
 * Prevents excessive custom events from being dispatched
 */

// Track the last time each event type was dispatched
const eventTimestamps: Record<string, number> = {};

// Minimum time between events (in milliseconds)
const MIN_EVENT_INTERVAL = 3000; // 3 seconds

/**
 * Safely dispatch a custom event with throttling
 * 
 * @param eventName - Name of the custom event
 * @param detail - Event detail object
 * @returns Boolean indicating if the event was dispatched
 */
export function safeDispatchEvent(eventName: string, detail: any = {}): boolean {
  const now = Date.now();
  const lastDispatch = eventTimestamps[eventName] || 0;
  const timeSinceLastDispatch = now - lastDispatch;
  
  // Check if we should throttle this event
  if (timeSinceLastDispatch < MIN_EVENT_INTERVAL) {
    console.log(`[EVENT-THROTTLER] Throttling ${eventName} event (${timeSinceLastDispatch}ms since last dispatch)`);
    return false;
  }
  
  // Update the timestamp
  eventTimestamps[eventName] = now;
  
  // Add a timestamp to the detail if not already present
  if (!detail.timestamp) {
    detail.timestamp = now;
  }
  
  // Dispatch the event
  try {
    const event = new CustomEvent(eventName, { detail });
    window.dispatchEvent(event);
    console.log(`[EVENT-THROTTLER] Dispatched ${eventName} event`);
    return true;
  } catch (error) {
    console.error(`[EVENT-THROTTLER] Error dispatching ${eventName} event:`, error);
    return false;
  }
}
