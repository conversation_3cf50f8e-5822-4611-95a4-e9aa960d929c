/**
 * NUCLEAR RATE LIMITER
 * Aggressively blocks excessive API calls from the same client
 */

// Store request timestamps by IP + endpoint
const requestLog: Record<string, number[]> = {};

// Maximum requests allowed in the time window
const MAX_REQUESTS_PER_WINDOW = 3;

// Time window in milliseconds (2 seconds)
const TIME_WINDOW_MS = 2000;

/**
 * Check if a request should be rate limited
 * @param ip Client IP address
 * @param endpoint API endpoint
 * @returns Boolean indicating if request should be blocked
 */
export function shouldRateLimit(ip: string, endpoint: string): boolean {
  const key = `${ip}:${endpoint}`;
  const now = Date.now();
  
  // Initialize request log for this client if it doesn't exist
  if (!requestLog[key]) {
    requestLog[key] = [];
  }
  
  // Filter out requests older than the time window
  requestLog[key] = requestLog[key].filter(timestamp => now - timestamp < TIME_WINDOW_MS);
  
  // Check if client has made too many requests
  if (requestLog[key].length >= MAX_REQUESTS_PER_WINDOW) {
    console.warn(`[RATE-LIMITER] Blocking excessive requests from ${ip} to ${endpoint}`);
    return true;
  }
  
  // Log this request
  requestLog[key].push(now);
  return false;
}

// Clean up old entries periodically
setInterval(() => {
  const now = Date.now();
  Object.keys(requestLog).forEach(key => {
    requestLog[key] = requestLog[key].filter(timestamp => now - timestamp < TIME_WINDOW_MS);
    if (requestLog[key].length === 0) {
      delete requestLog[key];
    }
  });
}, 30000); // Clean up every 30 seconds
