'use client';

import React, { createContext, useContext, useEffect, useState, PropsWithChildren } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { useRouter } from 'next/navigation';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { toast } from 'sonner';

type AuthContextType = {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<void>;
  getAuthToken: () => string | null;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: PropsWithChildren) {
  const supabase = useSupabaseClient();
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const refreshSession = async () => {
    try {
      console.log('[auth-provider] Refreshing session');
      const { data, error } = await supabase.auth.getSession();
      if (error) {
        console.error('[auth-provider] Error refreshing session:', error.message);
        throw error;
      }
      
      setSession(data.session);
      setUser(data.session?.user ?? null);
      
      if (data.session) {
        console.log('[auth-provider] Session found - user is authenticated');
      } else {
        console.log('[auth-provider] No active session found');
      }
    } catch (err: any) {
      console.error('[auth-provider] Error refreshing session:', err.message);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Initial load - refresh the session only once
    refreshSession();

    // Set up subscription to auth state changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (_event, newSession) => {
      console.log('[auth-provider] Auth state changed:', _event);
      
      // Update local state
      setSession(newSession);
      setUser(newSession?.user ?? null);
      setIsLoading(false);

      if (_event === 'SIGNED_IN') {
        console.log('[auth-provider] User signed in');
        toast.success('Signed in successfully');
        // Don't force navigation - let middleware handle it
      } else if (_event === 'SIGNED_OUT') {
        console.log('[auth-provider] User signed out');
        // Only redirect if currently on a protected page
        const isProtectedPage = 
          window.location.pathname.startsWith('/dashboard') ||
          window.location.pathname.startsWith('/project') ||
          window.location.pathname.startsWith('/profile');
          
        if (isProtectedPage) {
          router.push('/login');
        }
      }
      
      // Skip TOKEN_REFRESHED events to prevent auto-refreshing issues
    });

    // Clean up subscription on unmount
    return () => {
      subscription.unsubscribe();
    };
  }, [router, supabase]);

  const signOut = async () => {
    try {
      console.log('[auth-provider] Signing out user');
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      // Local state will be updated by the onAuthStateChange handler
      toast.success('Signed out successfully');
    } catch (err: any) {
      console.error('[auth-provider] Error signing out:', err.message);
      toast.error('Error signing out: ' + err.message);
    }
  };

  // Enhanced getAuthToken to try multiple methods
  const getAuthToken = () => {
    // First try session token
    if (session?.access_token) {
      return session.access_token;
    }
    
    // Then try localStorage (client-side only)
    if (typeof window !== 'undefined') {
      try {
        const supabaseKey = Object.keys(localStorage).find(key => key.startsWith('sb-'));
        if (supabaseKey) {
          const supabaseData = JSON.parse(localStorage.getItem(supabaseKey) || '{}');
          if (supabaseData?.access_token) {
            return supabaseData.access_token;
          }
        }
      } catch (e) {
        console.error('[auth-provider] Error accessing localStorage:', e);
      }
    }
    
    // Return null if no token found
    return null;
  };

  return (
    <AuthContext.Provider
      value={{ 
        user, 
        session, 
        isLoading, 
        signOut, 
        refreshSession, 
        getAuthToken 
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
