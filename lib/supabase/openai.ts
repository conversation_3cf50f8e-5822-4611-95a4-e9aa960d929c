/**
 * openai.ts
 * Utility functions for interacting with the OpenAI API
 * Used for REWRITE™ and BookMemory™ features
 */

// For now, we'll use the fetch API directly to call OpenAI
// In a production app, you might want to use the official OpenAI SDK
// or handle this on the server side for security

interface OpenAIResponse {
  choices: {
    message: {
      content: string;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

/**
 * Send a prompt to OpenAI's GPT-4o model and return the response
 * @param prompt The full prompt to send to OpenAI
 * @returns The AI response text and token usage
 */
export async function callOpenAI(
  prompt: string
): Promise<{ text: string; tokensUsed: number }> {
  // In a real app, you'd get this from an environment variable
  // and preferably handle API calls from a secure server endpoint
  const OPENAI_API_KEY = process.env.NEXT_PUBLIC_OPENAI_API_KEY;
  
  if (!OPENAI_API_KEY) {
    throw new Error('OpenAI API key is not configured');
  }
  
  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENAI_API_KEY}`
      },
      body: JSON.stringify({
        model: 'gpt-4o', // Use GPT-4o as specified in the RMD
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7
      })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`OpenAI API error: ${errorData.error?.message || response.statusText}`);
    }
    
    const data = await response.json() as OpenAIResponse;
    
    return {
      text: data.choices[0]?.message?.content || '',
      tokensUsed: data.usage?.total_tokens || 0
    };
  } catch (error: any) {
    console.error('Error calling OpenAI API:', error);
    throw new Error(`Failed to get AI response: ${error.message}`);
  }
}

/**
 * Generate the full prompt for REWRITE™ - Auto mode
 * @param markdownContent The markdown content to rewrite
 * @param bookMemoryContext Optional BookMemory™ context for paid users
 * @returns The complete prompt string
 */
export function generateAutoRewritePrompt(
  markdownContent: string,
  bookMemoryContext?: string
): string {
  let prompt = "Rewrite the following passage to improve clarity, pacing, and emotional depth. Maintain the original tone and intent. Return polished narrative suitable for fiction readers:\n\n";
  
  // Add BookMemory™ context if available (for paid users)
  if (bookMemoryContext) {
    prompt += "Context for consistency (use this to maintain story continuity):\n";
    prompt += bookMemoryContext + "\n\n";
  }
  
  // Add the actual content to rewrite
  prompt += "Passage to rewrite:\n";
  prompt += markdownContent;
  
  return prompt;
}

/**
 * Generate the full prompt for REWRITE™ - Custom mode
 * @param markdownContent The markdown content to rewrite
 * @param customStyle The user's style preferences
 * @param bookMemoryContext Optional BookMemory™ context for paid users
 * @returns The complete prompt string
 */
export function generateCustomRewritePrompt(
  markdownContent: string,
  customStyle: string,
  bookMemoryContext?: string
): string {
  let prompt = `Rewrite the following passage using the style described: ${customStyle}. Maintain original meaning unless improvement is necessary. Output should be structured, readable, and follow storytelling best practices:\n\n`;
  
  // Add BookMemory™ context if available (for paid users)
  if (bookMemoryContext) {
    prompt += "Context for consistency (use this to maintain story continuity):\n";
    prompt += bookMemoryContext + "\n\n";
  }
  
  // Add the actual content to rewrite
  prompt += "Passage to rewrite:\n";
  prompt += markdownContent;
  
  return prompt;
}

/**
 * Generate the full prompt for BookMemory™ chapter summarization
 * @param markdownContent The markdown content to summarize
 * @returns The complete prompt string
 */
export function generateBookMemoryPrompt(markdownContent: string): string {
  return `Summarize this chapter in 3–5 concise sentences focusing on key plot events, character progression, setting changes, and thematic elements introduced or developed. Keep the summary neutral and objective, designed to track story continuity for AI context:\n\n${markdownContent}`;
}