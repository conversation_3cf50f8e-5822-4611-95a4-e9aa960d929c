/**
 * bookmemory.ts
 * Functions for handling BookMemory™ automatic summarization
 */

import { htmlToMarkdown } from './markdown-conversion';
import { generateBookMemoryPrompt } from './openai';
import { getChapterMemory, createOrUpdateBookMemory } from './database';
import { fetchJson, postJson } from '@/lib/utils/auth-helpers';

/**
 * Process chapter content and generate or update BookMemory™ summary
 * This is called whenever a chapter is saved (for paid users)
 *
 * @param userId User ID for checking premium status
 * @param projectId Project ID
 * @param chapterId Chapter ID
 * @param content Chapter content in HTML format
 * @returns True if BookMemory™ was generated, false if user is not premium or other error
 */
export async function processBookMemory(
  userId: string,
  projectId: string,
  chapterId: string,
  content: string
): Promise<boolean> {
  // Validate inputs
  if (!userId || !projectId || !chapterId || !content) {
    console.error('BookMemory™ error: Missing required parameters');
    return false;
  }

  try {
    // 1. Check if user is on paid plan using our enhanced fetch utility
    const userData = await fetch<PERSON><PERSON><any>('/api/user-credits')
      .catch(error => {
        console.error('Failed to check user plan:', error);
        return null;
      });

    // If we couldn't get user data or user is not premium, skip BookMemory
    if (!userData || !userData.isPaidUser || !userData.bookMemoryEnabled) {
      console.log('BookMemory™ skipped: User is not on premium plan');
      return false;
    }

    // 2. Convert HTML to Markdown
    const markdownContent = htmlToMarkdown(content);

    // 3. Call the BookMemory™ API to generate summary using our enhanced post utility
    const bookMemoryResult = await postJson<any, any>(
      '/api/book-memory',
      {
        htmlContent: content,
        projectId,
        chapterId,
      }
    ).catch(error => {
      console.error('Failed to generate BookMemory™:', error);
      return null;
    });

    // If the API call failed, return false
    if (!bookMemoryResult) {
      return false;
    }

    // Success
    console.log('BookMemory™ generated successfully');
    return true;
  } catch (error) {
    console.error('Error processing BookMemory™:', error);
    return false;
  }
}

/**
 * Get BookMemory™ context for a specific project
 * Used for REWRITE™ to maintain consistency
 *
 * @param projectId Project ID
 * @param currentChapterId Current chapter ID (for context)
 * @returns BookMemory™ context string, empty if user is not premium or other error
 */
export async function getBookMemoryContext(
  projectId: string,
  currentChapterId: string
): Promise<string> {
  // Validate inputs
  if (!projectId) {
    console.error('BookMemory™ context error: Missing project ID');
    return '';
  }

  try {
    // 1. Check if user is on paid plan using our enhanced fetch utility
    const userData = await fetchJson<any>('/api/user-credits')
      .catch(error => {
        console.error('Failed to check user plan:', error);
        return null;
      });

    // If we couldn't get user data or user is not premium, return empty context
    if (!userData || !userData.isPaidUser || !userData.bookMemoryEnabled) {
      return '';
    }

    // 2. Fetch BookMemory™ entries for this project using our enhanced fetch utility
    const bookMemoryData = await fetchJson<any>(`/api/book-memory?projectId=${projectId}`)
      .catch(error => {
        console.error('Failed to fetch BookMemory entries:', error);
        return null;
      });

    // If we couldn't get BookMemory data or there are no memories, return empty context
    if (!bookMemoryData || !bookMemoryData.memories || bookMemoryData.memories.length === 0) {
      return '';
    }

    const { memories } = bookMemoryData;

    // 3. Build context string
    let context = 'Project Memory Context:\n\n';

    // First, add the current chapter's memory if it exists
    const currentChapterMemory = memories.find((m: any) => m.chapter_id === currentChapterId);
    if (currentChapterMemory) {
      context += `Current chapter: ${currentChapterMemory.summary_text}\n\n`;
    }

    // Then add other chapters (limit to 5 most recent, excluding current)
    const otherMemories = memories
      .filter((m: any) => m.chapter_id !== currentChapterId)
      .slice(0, 5);

    if (otherMemories.length > 0) {
      context += 'Previous chapters context:\n';

      otherMemories.forEach((memory: any, index: number) => {
        context += `Chapter ${memory.chapters?.chapter_order || index + 1}: ${memory.summary_text}\n`;
      });
    }

    return context;
  } catch (error) {
    console.error('Error getting BookMemory™ context:', error);
    return '';
  }
}