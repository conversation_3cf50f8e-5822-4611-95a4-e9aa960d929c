import { supabase } from './client';
import { Book, Chapter, MagicEdit, BookMemory } from '../types';
import { optimizeImage } from './image-processing';

// Project-related functions
export const createProject = async (
  title: string, 
  userId: string, 
  targetWordCount: number, 
  coverImageUrl?: string
): Promise<Book | null> => {
  try {
    const { data, error } = await supabase
      .from('projects')
      .insert({
        title,
        user_id: userId,
        goal: targetWordCount,
        cover_image_url: coverImageUrl || null,
        // current_word_count column doesn't exist yet in the database schema
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error: any) {
    console.error('Error creating project:', error.message);
    return null;
  }
};

export const getUserProjects = async (userId: string): Promise<Book[]> => {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error: any) {
    console.error('Error fetching user projects:', error.message);
    return [];
  }
};

export const getProjectById = async (projectId: string): Promise<Book | null> => {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .single();

    if (error) throw error;
    return data;
  } catch (error: any) {
    console.error('Error fetching project:', error.message);
    return null;
  }
};

export const updateProjectWordCount = async (projectId: string, wordCount: number): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('projects')
      .update({
        current_word_count: wordCount,
        updated_at: new Date().toISOString()
      })
      .eq('id', projectId);

    if (error) {
      // Log the specific error and return false
      console.error('Supabase error updating project word count:', error.message);
      return false;
    }

    return true; // Indicate success
  } catch (error: any) {
    // Catch any other unexpected errors
    console.error('Unexpected error in updateProjectWordCount:', error.message);
    return false;
  }
};

export const updateProjectGoal = async (projectId: string, goal: number): Promise<boolean> => {
  try {
    if (goal <= 0) {
      throw new Error('Word goal must be greater than zero');
    }
    
    const { error } = await supabase
      .from('projects')
      .update({
        goal: goal,
        updated_at: new Date().toISOString()
      })
      .eq('id', projectId);

    if (error) {
      console.error('Supabase error updating project goal:', error.message);
      return false;
    }

    return true;
  } catch (error: any) {
    console.error('Error updating project goal:', error.message);
    return false;
  }
};

export const deleteProject = async (projectId: string): Promise<boolean> => {
  try {
    // Delete all chapters first
    await supabase
      .from('chapters')
      .delete()
      .eq('project_id', projectId); // Changed from book_id to project_id

    // Then delete the project
    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', projectId);

    if (error) throw error;
    return true;
  } catch (error: any) {
    console.error('Error deleting project:', error.message);
    return false;
  }
};

/**
 * Update a project's cover image
 * @param projectId The project ID
 * @param file The new cover image file
 * @param currentCoverUrl The current cover image URL (for deletion)
 * @returns Object with the new URL and any error
 */
export const updateProjectCover = async (
  projectId: string,
  file: File,
  currentCoverUrl: string | null
): Promise<{ newUrl: string | null; error: any }> => {
  try {
    console.log('Starting cover image update process');
    
    // Step 1: Optimize the image
    console.log('Optimizing image...');
    const optimizedImage = await optimizeImage(file);
    
    // Convert Blob to File for upload
    const optimizedFile = new File(
      [optimizedImage], 
      file.name, 
      { type: file.type }
    );
    
    console.log(`Image optimized: Original size=${file.size}, Optimized size=${optimizedFile.size}`);
    
    // Step 2: Upload the new image
    console.log('Uploading optimized image...');
    const fileExt = file.name.split('.').pop();
    const fileName = `project_${projectId}_${Date.now()}.${fileExt}`;
    const filePath = `covers/${fileName}`;
    
    const { error: uploadError } = await supabase.storage
      .from('book-covers')
      .upload(filePath, optimizedFile, {
        contentType: file.type
      });
    
    if (uploadError) {
      console.error('Error uploading cover image:', uploadError);
      return { newUrl: null, error: uploadError };
    }
    
    // Step 3: Get the public URL
    const { data } = supabase.storage
      .from('book-covers')
      .getPublicUrl(filePath);
    
    const newPublicUrl = data.publicUrl;
    console.log('Image uploaded, new URL:', newPublicUrl);
    
    // Step 4: Update the project record
    console.log('Updating project database record...');
    const { error: dbError } = await supabase
      .from('projects')
      .update({ 
        cover_image_url: newPublicUrl, 
        updated_at: new Date().toISOString() 
      })
      .eq('id', projectId);
    
    if (dbError) {
      console.error('Error updating project record:', dbError);
      return { newUrl: newPublicUrl, error: dbError };
    }
    
    // Step 5: Delete the old image (if there was one)
    if (currentCoverUrl) {
      console.log('Deleting old cover image...');
      try {
        // Extract the path from the URL
        const pathParts = currentCoverUrl.split('/');
        const bucketName = pathParts[pathParts.length - 2];
        const objectPath = pathParts[pathParts.length - 1];
        
        if (bucketName && objectPath) {
          await supabase.storage
            .from('book-covers')
            .remove([`covers/${objectPath}`]);
          
          console.log('Old cover image deleted');
        }
      } catch (deleteError) {
        // Don't fail the whole operation if deletion fails
        console.warn('Warning: Failed to delete old cover image:', deleteError);
      }
    }
    
    console.log('Cover update process completed successfully');
    return { newUrl: newPublicUrl, error: null };
  } catch (error) {
    console.error('Error in updateProjectCover:', error);
    return { newUrl: null, error };
  }
};

// Chapter-related functions
export const createChapter = async (
  bookId: string,
  title: string,
  chapterOrder: number, // Renamed parameter for clarity
  content: string = '',
  subtitle: string | null = null
): Promise<Chapter | null> => {
  try {
    const wordCount = content.trim().split(/\s+/).filter(Boolean).length;

    const { data, error } = await supabase
      .from('chapters')
      .insert({
        project_id: bookId, // Changed from book_id to project_id
        title,
        content,
        chapter_order: chapterOrder, // CORRECTED: Using chapter_order instead of order
        word_count: wordCount,
        subtitle
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error: any) {
    console.error('Error creating chapter:', error.message);
    return null;
  }
};

export const getBookChapters = async (bookId: string): Promise<Chapter[]> => {
  try {
    const { data, error } = await supabase
      .from('chapters')
      .select('*')
      .eq('project_id', bookId) // Changed from book_id to project_id
      .order('chapter_order', { ascending: true }); // CORRECTED: Using chapter_order instead of order

    if (error) throw error;
    return data || [];
  } catch (error: any) {
    console.error('Error fetching book chapters:', error.message);
    return [];
  }
};

export const updateChapter = async (
  chapterId: string,
  content: string,
  title?: string,
  subtitle?: string | null
): Promise<Chapter | null> => {
  try {
    const wordCount = content.trim().split(/\s+/).filter(Boolean).length;
    
    const updates: any = {
      content,
      word_count: wordCount,
      updated_at: new Date().toISOString()
    };
    
    if (title) updates.title = title;
    if (subtitle !== undefined) updates.subtitle = subtitle;

    const { data, error } = await supabase
      .from('chapters')
      .update(updates)
      .eq('id', chapterId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error: any) {
    console.error('Error updating chapter:', error.message);
    return null;
  }
};

export const deleteChapter = async (chapterId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('chapters')
      .delete()
      .eq('id', chapterId);

    if (error) throw error;
    return true;
  } catch (error: any) {
    console.error('Error deleting chapter:', error.message);
    return false;
  }
};

// Storage functions for book cover images
export const uploadCoverImage = async (userId: string, file: File): Promise<string | null> => {
  try {
    // Optimize the image first
    const optimizedImage = await optimizeImage(file);
    
    // Convert Blob to File for upload
    const optimizedFile = new File(
      [optimizedImage], 
      file.name, 
      { type: file.type }
    );
    
    const fileExt = file.name.split('.').pop();
    const fileName = `${userId}_${Date.now()}.${fileExt}`;
    const filePath = `covers/${fileName}`;

    const { error } = await supabase.storage
      .from('book-covers')
      .upload(filePath, optimizedFile, {
        contentType: file.type
      });

    if (error) throw error;

    const { data } = supabase.storage
      .from('book-covers')
      .getPublicUrl(filePath);

    return data.publicUrl;
  } catch (error: any) {
    console.error('Error uploading cover image:', error.message);
    return null;
  }
};

export const deleteCoverImage = async (url: string): Promise<boolean> => {
  try {
    // Extract the path from the URL
    const path = url.split('/').slice(-2).join('/');
    
    const { error } = await supabase.storage
      .from('book-covers')
      .remove([path]);

    if (error) throw error;
    return true;
  } catch (error: any) {
    console.error('Error deleting cover image:', error.message);
    return false;
  }
};

// Helper to calculate total word count for a project
export const calculateProjectWordCount = async (projectId: string): Promise<number> => {
  try {
    const chapters = await getBookChapters(projectId);
    const totalWordCount = chapters.reduce((total, chapter) => total + chapter.word_count, 0);
    
    // The updateProjectWordCount function currently doesn't update the word count
    // since the current_word_count column doesn't exist yet in the database schema
    await updateProjectWordCount(projectId, totalWordCount);
    
    return totalWordCount;
  } catch (error: any) {
    console.error('Error calculating project word count:', error.message);
    return 0;
  }
};

// Magic Edit™ related functions

// Helper function to create a Magic Edit record
export const createMagicEdit = async (
  userId: string,
  projectId: string,
  chapterId: string,
  wordCount: number,
  tokensUsed: number
): Promise<any | null> => {
  try {
    const { data, error } = await supabase
      .from('magic_edits')
      .insert({
        user_id: userId,
        project_id: projectId,
        chapter_id: chapterId,
        word_count: wordCount,
        tokens_used: tokensUsed
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error: any) {
    console.error('Error creating Magic Edit record:', error.message);
    return null;
  }
};

// Gets user's Magic Edit history
export const getUserMagicEdits = async (
  userId: string,
  startDate?: Date,
  endDate?: Date
): Promise<any[]> => {
  try {
    let query = supabase
      .from('magic_edits')
      .select('*')
      .eq('user_id', userId);
    
    if (startDate) {
      query = query.gte('created_at', startDate.toISOString());
    }
    
    if (endDate) {
      query = query.lte('created_at', endDate.toISOString());
    }
    
    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error: any) {
    console.error('Error fetching user Magic Edits:', error.message);
    return [];
  }
};

// Gets the count of Magic Edits
export const getMagicEditsCount = async (
  userId: string,
  startDate?: Date,
  endDate?: Date
): Promise<number> => {
  try {
    console.log('[getMagicEditsCount] Starting Magic Edit count calculation', {
      userId,
      startDate: startDate?.toISOString(),
      endDate: endDate?.toISOString()
    });

    let query = supabase
      .from('magic_edits')
      .select('id', { count: 'exact' })
      .eq('user_id', userId);
    
    if (startDate) {
      query = query.gte('created_at', startDate.toISOString());
    }
    
    if (endDate) {
      query = query.lte('created_at', endDate.toISOString());
    }
    
    console.log('[getMagicEditsCount] Executing query');
    const { count, error } = await query;

    if (error) {
      console.error('[getMagicEditsCount] Error from Supabase:', error);
      throw error;
    }
    
    console.log('[getMagicEditsCount] Result:', count || 0);
    return count || 0;
  } catch (error: any) {
    console.error('[getMagicEditsCount] Error counting user Magic Edits:', error.message);
    // Always return 0 if there's an error to ensure the application doesn't break
    return 0;
  }
};

// Gets the count of Magic Edits used in the current month
export const getMonthlyMagicEditsCount = async (userId: string): Promise<number> => {
  try {
    console.log('[getMonthlyMagicEditsCount] Calculating monthly Magic Edit count for', userId);
    
    // Validation
    if (!userId) {
      console.log('[getMonthlyMagicEditsCount] Invalid userId provided');
      return 0;
    }
    
    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    console.log('[getMonthlyMagicEditsCount] First day of month:', firstDayOfMonth.toISOString());
    
    // Query magic_edits table for entries in the current month
    const { count, error } = await supabase
      .from('magic_edits')
      .select('id', { count: 'exact' })
      .eq('user_id', userId)
      .gte('created_at', firstDayOfMonth.toISOString());
    
    if (error) {
      console.error('[getMonthlyMagicEditsCount] Error from Supabase:', error);
      throw error;
    }
    
    console.log('[getMonthlyMagicEditsCount] Result:', count || 0);
    return count || 0;
  } catch (error: any) {
    console.error('[getMonthlyMagicEditsCount] Error:', error.message);
    return 0;
  }
};

// Store chapter edit progress
export const storeChapterEditProgress = async (
  chapterId: string,
  editedSections: Array<{
    start_index: number, 
    end_index: number, 
    status: 'edited' | 'accepted' | 'pending',
    timestamp?: string
  }>
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('chapter_edit_progress')
      .upsert({
        chapter_id: chapterId,
        edited_sections: editedSections,
        last_updated: new Date().toISOString()
      }, {
        onConflict: 'chapter_id'
      });

    if (error) throw error;
    return true;
  } catch (error: any) {
    console.error('Error storing chapter edit progress:', error.message);
    return false;
  }
};

// Get chapter edit progress
export const getChapterEditProgress = async (chapterId: string): Promise<any> => {
  try {
    const { data, error } = await supabase
      .from('chapter_edit_progress')
      .select('*')
      .eq('chapter_id', chapterId)
      .single();

    if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "No rows returned"
    return data || { chapter_id: chapterId, edited_sections: [], last_updated: null };
  } catch (error: any) {
    console.error('Error fetching chapter edit progress:', error.message);
    return { chapter_id: chapterId, edited_sections: [], last_updated: null };
  }
};

// BookMemory™ related functions
export const createOrUpdateBookMemory = async (
  projectId: string,
  chapterId: string,
  summaryText: string
): Promise<BookMemory | null> => {
  try {
    // Check if a memory already exists for this chapter
    const { data: existingMemory, error: fetchError } = await supabase
      .from('book_memory')
      .select('*')
      .eq('chapter_id', chapterId)
      .maybeSingle();
    
    if (fetchError) throw fetchError;
    
    if (existingMemory) {
      // Update existing memory
      const { data, error } = await supabase
        .from('book_memory')
        .update({
          summary_text: summaryText,
          created_at: new Date().toISOString() // Update timestamp
        })
        .eq('id', existingMemory.id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    } else {
      // Create new memory
      const { data, error } = await supabase
        .from('book_memory')
        .insert({
          project_id: projectId,
          chapter_id: chapterId,
          summary_text: summaryText
        })
        .select()
        .single();
      
      if (error) throw error;
      return data;
    }
  } catch (error: any) {
    console.error('Error creating/updating book memory:', error.message);
    return null;
  }
};

export const getProjectMemories = async (projectId: string): Promise<BookMemory[]> => {
  try {
    const { data, error } = await supabase
      .from('book_memory')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  } catch (error: any) {
    console.error('Error fetching project memories:', error.message);
    return [];
  }
};

export const getChapterMemory = async (chapterId: string): Promise<BookMemory | null> => {
  try {
    const { data, error } = await supabase
      .from('book_memory')
      .select('*')
      .eq('chapter_id', chapterId)
      .single();
    
    if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "No rows returned"
    return data || null;
  } catch (error: any) {
    console.error('Error fetching chapter memory:', error.message);
    return null;
  }
};
