/**
 * Unified authentication utilities for the BookWriter application
 * This file provides centralized authentication functions for both client and server-side
 */

import {
  createServerComponentClient,
  createRouteHandlerClient,
  createMiddlewareClient
} from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { User } from '@supabase/supabase-js';
import { Database } from './database.types';

/**
 * Creates a server-side Supabase client with proper cookie handling
 * Use this for server components
 */
export function createServerSupabaseClient() {
  return createServerComponentClient<Database>({ cookies });
}

/**
 * Creates a route handler Supabase client
 * Use this for API routes
 */
export function createRouteSupabaseClient() {
  return createRouteHandlerClient<Database>({ cookies });
}

/**
 * Creates a middleware Supabase client
 * Use this specifically in the middleware.ts file
 */
export function createMiddlewareSupabaseClient(req: NextRequest, res: NextResponse) {
  return createMiddlewareClient<Database>({ req, res });
}

/**
 * Server-side authentication helper for API routes
 * Returns authenticated user data or handles the error response
 */
export async function authenticateApiRequest(request: NextRequest) {
  console.log('[auth-utils] Authenticating API request');
  
  // Create Supabase client with cookie handling
  const supabase = createRouteSupabaseClient();
  
  // Check for JWT token authentication first (for programmatic clients)
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    console.log('[auth-utils] Attempting JWT token authentication');
    const token = authHeader.split(' ')[1];
    
    try {
      const { data, error } = await supabase.auth.getUser(token);
      
      if (error || !data.user) {
        console.error('[auth-utils] JWT token authentication failed:', error);
        return {
          user: null,
          supabase,
          authError: error,
          handleAuthError: () => NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
        };
      }
      
      console.log('[auth-utils] JWT authentication successful');
      return {
        user: data.user,
        supabase,
        authError: null,
        handleAuthError: () => null
      };
    } catch (error) {
      console.error('[auth-utils] Error during JWT authentication:', error);
      return {
        user: null,
        supabase,
        authError: error,
        handleAuthError: () => NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      };
    }
  }
  
  // Fallback to cookie authentication
  console.log('[auth-utils] Attempting cookie-based authentication');
  try {
    const { data, error } = await supabase.auth.getUser();
    
    if (error || !data.user) {
      console.error('[auth-utils] Cookie authentication failed:', error);
      return {
        user: null,
        supabase,
        authError: error || new Error('No authenticated user found'),
        handleAuthError: () => NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      };
    }
    
    console.log('[auth-utils] Cookie authentication successful');
    return {
      user: data.user,
      supabase,
      authError: null,
      handleAuthError: () => null
    };
  } catch (error) {
    console.error('[auth-utils] Error during cookie authentication:', error);
    return {
      user: null,
      supabase,
      authError: error,
      handleAuthError: () => NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    };
  }
}

/**
 * Helper function to get the current authenticated user
 * Returns the user object and error if any
 */
export async function getCurrentUser() {
  const supabase = createServerComponentClient<Database>({ cookies });
  return await supabase.auth.getUser();
}

/**
 * Helper to check if a user is authenticated and get their data
 * Returns null if user is not authenticated
 */
export async function getAuthenticatedUserOrNull(): Promise<User | null> {
  try {
    const { data, error } = await getCurrentUser();
    if (error || !data.user) {
      return null;
    }
    return data.user;
  } catch (error) {
    console.error('[auth-utils] Error getting authenticated user:', error);
    return null;
  }
}