/**
 * Authentication utilities for API routes
 * Handles both JWT token and session-based authentication
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Database } from './database.types';

/**
 * Unified authentication function for API routes
 * Handles both JWT token auth (from Authorization header) and cookie-based auth
 * 
 * @param request The Next.js request object
 * @returns Object containing the user (if authenticated), Supabase client, error, and error handler
 */
export async function authenticateApiRequest(request: NextRequest) {
  console.log('[api-auth] Authenticating API request');

  // First try JWT token authentication (from Authorization header)
  const authHeader = request.headers.get('authorization');
  // Also check for custom JWT header
  const customJwtHeader = request.headers.get('x-supabase-jwt');
  let authError = null;
  let user = null;
  
  console.log('[api-auth] Auth headers found:', {
    hasAuthHeader: !!authHeader,
    hasCustomJwtHeader: !!customJwtHeader
  });
  
  let supabase: ReturnType<typeof createClient<Database>> | null = null;
  let jwt: string | null = null;

  if (authHeader && authHeader.startsWith('Bearer ')) {
    console.log('[api-auth] Found Authorization header, attempting JWT token authentication');
    
    // Extract the JWT token
    const token = authHeader.split(' ')[1];
    
    try {
      // Get the current user with the token
      jwt = token;
      const tempClient = createClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          global: {
            headers: {
              Authorization: `Bearer ${jwt}`
            }
          }
        }
      );
      const { data, error } = await tempClient.auth.getUser();
      supabase = tempClient;
      
      if (error) {
        console.error('[api-auth] JWT token authentication failed:', error);
        authError = error;
      } else if (data?.user) {
        console.log('[api-auth] JWT token authentication successful');
        user = data.user;
      }
    } catch (error) {
      console.error('[api-auth] Error during JWT token authentication:', error);
      authError = error;
    }
  } 
  
  // If first method failed, try custom header
  if (!user && customJwtHeader) {
    console.log('[api-auth] Attempting authentication with x-supabase-jwt header');
    
    try {
      // Get the current user with the custom header token
      jwt = customJwtHeader;
      const tempClient = createClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          global: {
            headers: {
              Authorization: `Bearer ${jwt}`
            }
          }
        }
      );
      const { data, error } = await tempClient.auth.getUser();
      supabase = tempClient;
      
      if (error) {
        console.error('[api-auth] x-supabase-jwt authentication failed:', error);
        authError = error;
      } else if (data?.user) {
        console.log('[api-auth] x-supabase-jwt authentication successful');
        user = data.user;
      }
    } catch (error) {
      console.error('[api-auth] Error during x-supabase-jwt authentication:', error);
      authError = error;
    }
  }
  
  // Check for user ID header as an extra auth method
  const userIdHeader = request.headers.get('x-user-id');
  // Temporarily commenting out this block due to issues querying auth.users with anon key
  // and to resolve persistent TypeScript errors. This auth path needs review.
  /*
  if (!user && userIdHeader) {
    console.log('[api-auth] Found x-user-id header, attempting direct user lookup');
    
    try {
      // Get the user directly by ID from Supabase
      const tempClient = createClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );
      // The following line causes a TypeScript error because 'users' is in the 'auth' schema,
      // not 'public', and this client defaults to 'public'.
      // Accessing auth.users directly with anon key is also generally problematic.
      const { data, error } = await tempClient
        .from('users') 
        .select('*')
        .eq('id', userIdHeader)
        .single();
      
      if (error) {
        console.error('[api-auth] User lookup by ID failed:', error);
      } else if (data) {
        console.log('[api-auth] User found by ID header:', userIdHeader);
        // Create a pseudo-user with the ID from the database
        user = { id: userIdHeader } as any; // Cast as User
      }
    } catch (error) {
      console.error('[api-auth] Error during user ID lookup:', error);
    }
  }
  */

  // If JWT authentication failed or wasn't attempted, fall back to session data
  if (!user) {
    console.log('[api-auth] Attempting session-based authentication');
    
    try {
      // Try to get the session directly without using cookies
      const tempClient = createClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );
      const { data, error } = await tempClient.auth.getSession();
      supabase = tempClient;
      
      if (error) {
        console.error('[api-auth] Session authentication failed:', error);
        authError = error;
      } else if (data?.session?.user) {
        console.log('[api-auth] Session authentication successful');
        user = data.session.user;
      } else {
        console.error('[api-auth] No valid session found');
        authError = new Error('No valid session found');
      }
    } catch (error) {
      console.error('[api-auth] Error during session authentication:', error);
      authError = error;
    }
  }
  
  // Create a helper function to handle auth errors
  const handleAuthError = () => {
    if (!user) {
      console.log('[api-auth] Authentication failed, returning 401 error');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    return null;
  };
  
  return {
    supabase,
    user,
    authError,
    handleAuthError
  };
}
