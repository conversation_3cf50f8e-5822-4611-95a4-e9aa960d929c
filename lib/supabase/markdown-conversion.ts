/**
 * markdown-conversion.ts
 * Utilities for converting between Tiptap HTML content and Markdown
 * for AI communication in REWRITE™ and BookMemory™ features
 */

// Use a lightweight HTML-to-Markdown converter
import TurndownService from 'turndown';
import { marked } from 'marked';

// Initialize turndown service with specific options
const turndownService = new TurndownService({
  headingStyle: 'atx',
  codeBlockStyle: 'fenced',
  emDelimiter: '*',
  strongDelimiter: '**'
});

// Customize rules for better conversion
turndownService.addRule('emphasis', {
  filter: ['em', 'i'],
  replacement: function(content) {
    return '*' + content + '*';
  }
});

turndownService.addRule('strong', {
  filter: ['strong', 'b'],
  replacement: function(content) {
    return '**' + content + '**';
  }
});

/**
 * Convert HTML content from the editor to Markdown for AI processing
 * @param htmlContent HTML string from the TipTap editor
 * @returns Markdown formatted string
 */
export function htmlToMarkdown(htmlContent: string): string {
  if (!htmlContent) return '';
  
  try {
    // Process the HTML to handle special cases
    const processedHtml = htmlContent
      // Replace paragraph breaks with double linebreaks for proper Markdown
      .replace(/<\/p><p>/g, '</p>\n\n<p>')
      // Replace <br> with newlines
      .replace(/<br\s*\/?>/g, '\n');
    
    // Convert to Markdown
    const markdown = turndownService.turndown(processedHtml);
    
    return markdown;
  } catch (error) {
    console.error('Error converting HTML to Markdown:', error);
    return htmlContent; // Return original content if conversion fails
  }
}

/**
 * Convert Markdown from AI back to HTML for the editor
 * @param markdownContent Markdown string from AI
 * @returns HTML formatted string ready for the editor
 */
export function markdownToHtml(markdownContent: string): string {
  if (!markdownContent) return '';
  
  try {
    // Configure marked options for better compatibility with TipTap
    marked.setOptions({
      headerIds: false,
      mangle: false,
      gfm: true,
      breaks: true,
    });
    
    // Convert Markdown to HTML
    const rawHtml = marked.parse(markdownContent);
    
    // Process the HTML for editor compatibility and formatting consistency
    const processedHtml = String(rawHtml)
      // Ensure paragraphs have proper spacing
      .replace(/<\/p>\s*<p>/g, '</p><p>')
      // Clean up any consecutive line breaks
      .replace(/\n{3,}/g, '\n\n')
      // Remove any style attributes to prevent formatting issues
      .replace(/\sstyle="[^"]*"/g, '')
      // Remove classes that might interfere with editor styling
      .replace(/\sclass="[^"]*"/g, '')
      // Replace deprecated elements with their HTML5 equivalents
      .replace(/<b>/g, '<strong>')
      .replace(/<\/b>/g, '</strong>')
      .replace(/<i>/g, '<em>')
      .replace(/<\/i>/g, '</em>')
      // Ensure empty paragraphs have a space to be properly displayed
      .replace(/<p><\/p>/g, '<p>&nbsp;</p>')
      // Normalize quotes and apostrophes
      .replace(/['']/g, "'")
      .replace(/[""]/g, '"');
    
    return processedHtml;
  } catch (error) {
    console.error('Error converting Markdown to HTML:', error);
    return `<p>${markdownContent}</p>`; // Simple fallback with paragraph tags
  }
}

/**
 * Count words in text (for validating against the 1,000 word limit)
 * @param text Plain text content
 * @returns Number of words
 */
export function countWords(text: string): number {
  if (!text) return 0;
  
  // Convert HTML to plain text if needed
  const plainText = text.replace(/<[^>]*>/g, ' ');
  
  // Split by whitespace and filter empty strings
  return plainText.trim().split(/\s+/).filter(Boolean).length;
}

/**
 * Truncate text to a maximum word count (for the 1,000 word REWRITE™ limit)
 * @param text Text to truncate
 * @param maxWordCount Maximum number of words to include
 * @returns Truncated text
 */
export function truncateToWordLimit(text: string, maxWordCount: number = 1000): string {
  if (!text) return '';
  
  const words = text.trim().split(/\s+/);
  if (words.length <= maxWordCount) return text;
  
  return words.slice(0, maxWordCount).join(' ') + '...';
}