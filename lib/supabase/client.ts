/**
 * Supabase client for browser usage
 * This client is used in client components where cookies are automatically handled
 */

import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from './database.types';

/**
 * Initialize the Supabase client for browser usage
 * This automatically handles cookies for authenticated requests
 * Note: This client should be used directly in client components
 * that aren't wrapped in the SessionContextProvider
 */
export const supabase = createClientComponentClient<Database>({
  options: {
    autoRefreshToken: false,
    persistSession: true
  }
});