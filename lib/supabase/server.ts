/**
 * Supabase client factory for server-side usage
 * This file exports a function that creates a server-side Supabase client
 * with proper cookie handling for authenticated requests
 */

import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Database } from './database.types';

/**
 * Creates a server-side Supabase client with proper cookie handling
 * Use this in server components where you need Supabase functionality
 * 
 * @returns A Supabase client configured for server component usage
 */
export function createClient() {
  return createServerComponentClient<Database>({ cookies });
}