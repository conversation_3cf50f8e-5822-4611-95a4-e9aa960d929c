import { NextRequest } from 'next/server';

/**
 * Enhanced fetch utility with error handling and auth support
 */
export async function fetchJson<T>(
  url: string, 
  options: RequestInit = {},
  token?: string
): Promise<T> {
  try {
    const headers = new Headers(options.headers || {});
    
    // Add content type if not already set
    if (!headers.get('Content-Type')) {
      headers.set('Content-Type', 'application/json');
    }
    
    // Add authorization header if token is provided
    if (token) {
      headers.set('Authorization', `Bearer ${token}`);
    }
    
    const response = await fetch(url, {
      ...options,
      headers
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Fetch error:', error);
    throw error;
  }
}

/**
 * Enhanced POST utility with auth support
 */
export async function postJson<T>(
  url: string, 
  body: any,
  options: RequestInit = {},
  token?: string
): Promise<T> {
  return fetchJson<T>(url, {
    ...options,
    method: 'POST',
    body: JSON.stringify(body)
  }, token);
}
