/**
 * data-fetching.ts
 * Robust data fetching utilities for reliable API interactions
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { toast } from 'sonner';

/**
 * Type for the result of a safe database operation
 */
export interface SafeQueryResult<T> {
  data: T | null;
  error: Error | null;
  success: boolean;
}

/**
 * Safely execute a Supabase query with comprehensive error handling
 * 
 * @param operation - Function that performs the Supabase query
 * @param errorMessage - User-friendly error message to display on failure
 * @param silent - Whether to suppress toast notifications
 * @returns SafeQueryResult with data, error, and success status
 */
export async function safeQuery<T>(
  operation: () => Promise<{ data: T | null; error: any }>,
  errorMessage: string = 'Database operation failed',
  silent: boolean = false
): Promise<SafeQueryResult<T>> {
  try {
    // Execute the operation
    const { data, error } = await operation();
    
    // Handle database errors
    if (error) {
      console.error(`Database error: ${errorMessage}`, error);
      
      if (!silent) {
        toast.error(errorMessage);
      }
      
      return {
        data: null,
        error: new Error(error.message || errorMessage),
        success: false
      };
    }
    
    // Success case
    return {
      data,
      error: null,
      success: true
    };
  } catch (error: any) {
    // Handle unexpected errors
    console.error(`Unexpected error: ${errorMessage}`, error);
    
    if (!silent) {
      toast.error(errorMessage);
    }
    
    return {
      data: null,
      error: new Error(error.message || 'Unknown error occurred'),
      success: false
    };
  }
}

/**
 * Safely fetch chapters for multiple projects
 * 
 * @param supabase - Supabase client
 * @param projectIds - Array of project IDs to fetch chapters for
 * @returns SafeQueryResult with chapters data
 */
export async function fetchProjectChapters(
  supabase: SupabaseClient,
  projectIds: string[]
): Promise<SafeQueryResult<any[]>> {
  if (!projectIds || projectIds.length === 0) {
    return {
      data: [],
      error: null,
      success: true
    };
  }
  
  return safeQuery(
    () => supabase
      .from('chapters')
      .select('*')
      .in('project_id', projectIds),
    'Failed to fetch project chapters',
    true // Silent - don't show toast for this operation
  );
}

/**
 * Safely fetch chapters for a single project
 * 
 * @param supabase - Supabase client
 * @param projectId - Project ID to fetch chapters for
 * @returns SafeQueryResult with chapters data
 */
export async function fetchSingleProjectChapters(
  supabase: SupabaseClient,
  projectId: string
): Promise<SafeQueryResult<any[]>> {
  if (!projectId) {
    return {
      data: [],
      error: null,
      success: true
    };
  }
  
  return safeQuery(
    () => supabase
      .from('chapters')
      .select('*')
      .eq('project_id', projectId),
    `Failed to fetch chapters for project ${projectId}`,
    true // Silent - don't show toast for this operation
  );
}
