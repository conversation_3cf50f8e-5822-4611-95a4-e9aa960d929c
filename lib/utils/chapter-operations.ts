/**
 * chapter-operations.ts
 * Robust utilities for chapter operations
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { safeQuery } from './data-fetching';
import { Chapter } from '@/lib/types';

/**
 * Update chapter content with robust error handling
 * 
 * @param supabase - Supabase client
 * @param chapterId - ID of the chapter to update
 * @param content - New content for the chapter
 * @param wordCount - Word count of the content
 * @returns Promise with the result of the operation
 */
export async function updateChapterContent(
  supabase: SupabaseClient,
  chapterId: string,
  content: string,
  wordCount: number
) {
  return safeQuery(
    () => supabase
      .from('chapters')
      .update({
        content,
        word_count: wordCount,
        updated_at: new Date().toISOString()
      })
      .eq('id', chapterId)
      .select()
      .single(),
    `Failed to update chapter ${chapterId}`
  );
}

/**
 * Calculate word count from HTML content
 * 
 * @param content - HTML content to count words in
 * @returns Number of words in the content
 */
export function calculateWordCount(content: string): number {
  if (!content) return 0;
  
  // Remove HTML tags
  const textOnly = content.replace(/<[^>]*>/g, ' ');
  
  // Split by whitespace and filter out empty strings
  return textOnly.trim().split(/\s+/).filter(Boolean).length;
}

/**
 * Update project word count based on chapters
 * 
 * @param supabase - Supabase client
 * @param projectId - ID of the project to update
 * @param chapters - Array of chapters to calculate word count from
 * @returns Promise with the result of the operation
 */
export async function updateProjectWordCount(
  supabase: SupabaseClient,
  projectId: string,
  chapters: Chapter[]
) {
  // Calculate total from all chapters with null safety
  const totalWords = chapters.reduce((sum, chapter) => {
    return sum + (typeof chapter.word_count === 'number' ? chapter.word_count : 0);
  }, 0);
  
  return safeQuery(
    () => supabase
      .from('projects')
      .update({
        current_word_count: totalWords,
        updated_at: new Date().toISOString()
      })
      .eq('id', projectId)
      .select()
      .single(),
    `Failed to update word count for project ${projectId}`
  );
}

/**
 * Fetch all chapters for a project
 * 
 * @param supabase - Supabase client
 * @param projectId - ID of the project to fetch chapters for
 * @returns Promise with the chapters
 */
export async function fetchProjectChapters(
  supabase: SupabaseClient,
  projectId: string
) {
  return safeQuery(
    () => supabase
      .from('chapters')
      .select('*')
      .eq('project_id', projectId)
      .order('chapter_order', { ascending: true }),
    `Failed to fetch chapters for project ${projectId}`
  );
}

/**
 * Fetch a project by ID
 * 
 * @param supabase - Supabase client
 * @param projectId - ID of the project to fetch
 * @returns Promise with the project
 */
export async function fetchProject(
  supabase: SupabaseClient,
  projectId: string
) {
  return safeQuery(
    () => supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .single(),
    `Failed to fetch project ${projectId}`
  );
}
