'use client';

/**
 * API client for making authenticated requests to our API endpoints
 * Automatically adds authentication headers and handles cookies
 */

export interface FetchOptions extends RequestInit {
  token?: string | null;
}

/**
 * Enhanced fetch that automatically adds auth headers if a token is provided
 * Always includes credentials for cookie-based auth as a fallback
 * 
 * @param url The URL to fetch
 * @param options Standard fetch options with optional token
 * @returns Promise with the fetch response
 */
export async function fetchWithAuth(url: string, options: FetchOptions = {}) {
  const { token, headers = {}, ...restOptions } = options;
  
  // Create headers object with auth token if provided
  const requestHeaders: HeadersInit = {
    'Content-Type': 'application/json',
    ...headers,
    // Add cache control to prevent browser caching
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
  };
  
  // Add authorization header if token is available
  if (token) {
    console.log('[fetchWithAuth] Using token authentication');
    requestHeaders['Authorization'] = `Bearer ${token}`;
  } else {
    console.log('[fetchWithAuth] Token not provided, relying on cookie-based auth');
    
    // Include session token from local storage as fallback
    try {
      // This is a reliable way to get the current session token
      const supabaseKey = Object.keys(localStorage).find(key => key.startsWith('sb-'));
      if (supabaseKey) {
        const supabaseData = JSON.parse(localStorage.getItem(supabaseKey) || '{}');
        if (supabaseData?.access_token) {
          console.log('[fetchWithAuth] Found supabase token in localStorage');
          requestHeaders['Authorization'] = `Bearer ${supabaseData.access_token}`;
        }
      }
    } catch (e) {
      console.error('[fetchWithAuth] Error accessing localStorage:', e);
    }
  }
  
  try {
    console.log(`[fetchWithAuth] Requesting: ${url}`);
    
    // Important: ALWAYS include credentials: 'include' to send cookies with the request
    // This ensures cookie-based auth works even if token auth fails or isn't provided
    const response = await fetch(url, {
      ...restOptions,
      headers: requestHeaders,
      credentials: 'include', // Ensure cookies are sent with the request
    });
    
    // Log the auth method used (for debugging)
    if (token) {
      console.log(`[fetchWithAuth] Request made with token auth, status: ${response.status}`);
    } else {
      console.log(`[fetchWithAuth] Request made with cookie auth, status: ${response.status}`);
    }
    
    return response;
  } catch (error) {
    console.error(`[fetchWithAuth] Fetch error for ${url}:`, error);
    // Rethrow the error after logging it
    throw error;
  }
}

/**
 * API client with methods for different HTTP methods
 * Use in components by importing: import { apiClient } from '@/lib/fetch-client';
 */
export const apiClient = {
  /**
   * Make a GET request to the API
   * 
   * @param url The URL to fetch
   * @param options Fetch options with optional token
   * @returns Promise with JSON response
   */
  async get<T>(url: string, options: FetchOptions = {}): Promise<T> {
    const response = await fetchWithAuth(url, {
      method: 'GET',
      ...options,
    });
    
    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      throw new Error(`API error (${response.status}): ${errorText}`);
    }
    
    return response.json();
  },
  
  /**
   * Make a POST request to the API
   * 
   * @param url The URL to fetch
   * @param data Data to send in the request body
   * @param options Fetch options with optional token
   * @returns Promise with JSON response
   */
  async post<T>(url: string, data: any, options: FetchOptions = {}): Promise<T> {
    const response = await fetchWithAuth(url, {
      method: 'POST',
      body: JSON.stringify(data),
      ...options,
    });
    
    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      throw new Error(`API error (${response.status}): ${errorText}`);
    }
    
    return response.json();
  },
  
  /**
   * Make a PUT request to the API
   * 
   * @param url The URL to fetch
   * @param data Data to send in the request body
   * @param options Fetch options with optional token
   * @returns Promise with JSON response
   */
  async put<T>(url: string, data: any, options: FetchOptions = {}): Promise<T> {
    const response = await fetchWithAuth(url, {
      method: 'PUT',
      body: JSON.stringify(data),
      ...options,
    });
    
    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      throw new Error(`API error (${response.status}): ${errorText}`);
    }
    
    return response.json();
  },
  
  /**
   * Make a DELETE request to the API
   * 
   * @param url The URL to fetch
   * @param options Fetch options with optional token
   * @returns Promise with JSON response
   */
  async delete<T>(url: string, options: FetchOptions = {}): Promise<T> {
    const response = await fetchWithAuth(url, {
      method: 'DELETE',
      ...options,
    });
    
    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      throw new Error(`API error (${response.status}): ${errorText}`);
    }
    
    return response.json();
  },
};