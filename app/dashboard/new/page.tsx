'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import * as z from 'zod';

import { supabase } from '@/lib/supabase/client';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Navbar } from '@/components/navbar';
import { useAuth } from '@/lib/providers/auth-provider';

const newProjectSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  author: z.string().min(1, 'Author is required'),
  word_goal: z.string().optional().transform(val => val ? parseInt(val) : undefined),
});

type NewProjectFormValues = z.infer<typeof newProjectSchema>;

export default function NewProjectPage() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { user } = useAuth();

  const form = useForm<NewProjectFormValues>({
    resolver: zodResolver(newProjectSchema),
    defaultValues: {
      title: '',
      author: '',
      word_goal: '',
    },
  });

  async function onSubmit(data: NewProjectFormValues) {
    if (!user) {
      toast.error('You must be logged in to create a project');
      return;
    }

    setIsLoading(true);
    
    try {
      // Insert new project
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .insert({
          title: data.title,
          author: data.author,
          user_id: user.id,
          word_goal: data.word_goal,
        })
        .select()
        .single();
      
      if (projectError) throw projectError;
      
      // Create initial chapter
      const { error: chapterError } = await supabase
        .from('chapters')
        .insert({
          project_id: projectData.id,
          title: 'Chapter 1',
          content: '',
          order: 1,
        });
      
      if (chapterError) throw chapterError;
      
      toast.success('Project created successfully');
      router.push(`/book/${projectData.id}/edit`);
    } catch (error: any) {
      toast.error(error.message || 'Failed to create project');
      console.error('Error creating project:', error);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Navbar />
      
      <main className="flex-1 py-16 px-4 md:px-6 mt-16">
        <div className="container max-w-md mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl font-serif">Create New Project</CardTitle>
              <CardDescription>Fill in the details to start your writing journey</CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Book Title</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Enter your book title" 
                            className="font-serif placeholder:font-serif" 
                            disabled={isLoading} 
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="author"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Author Name</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Your name or pen name" 
                            className="font-serif placeholder:font-serif" 
                            disabled={isLoading} 
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="word_goal"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Word Goal (Optional)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            placeholder="e.g., 50000" 
                            className="font-serif placeholder:font-serif" 
                            disabled={isLoading} 
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          Set a target word count for your book
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Button 
                    type="submit" 
                    className="w-full font-serif" 
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating project...
                      </>
                    ) : (
                      'Create Project'
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
            <CardFooter className="flex justify-center">
              <Button 
                variant="ghost" 
                onClick={() => router.push('/dashboard')}
                disabled={isLoading}
              >
                Cancel
              </Button>
            </CardFooter>
          </Card>
        </div>
      </main>
    </div>
  );
}