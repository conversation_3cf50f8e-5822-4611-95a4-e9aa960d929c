"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { GoldProgress } from "@/components/ui/gold-progress"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/lib/providers/auth-provider"
import { getUserProjects, uploadCoverImage, createProject, createChapter } from "@/lib/supabase/database"
import { Book } from "@/lib/types"
import { Loader2, Plus, BookOpen, FileImage, Target, Sparkles, Settings } from "lucide-react"
import { useProjectProgress } from "@/hooks/use-project-progress-fixed"

export default function DashboardPage() {
  const [books, setBooks] = useState<Book[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [coverFile, setCoverFile] = useState<File | null>(null)
  const [coverPreview, setCoverPreview] = useState<string | null>(null)

  // Form fields
  const [newBookTitle, setNewBookTitle] = useState("")
  const [newBookWordGoal, setNewBookWordGoal] = useState("")

  const router = useRouter()
  const { user } = useAuth()
  const { toast } = useToast()

  // Get real-time project progress
  const { projectProgress, loading: progressLoading } = useProjectProgress(books, user?.id)

  // Load user's books
  useEffect(() => {
    const loadBooks = async () => {
      if (!user) return

      setIsLoading(true)
      const userProjects = await getUserProjects(user.id)
      setBooks(userProjects)
      setIsLoading(false)

      // If user has no books, show create modal automatically
      if (userProjects.length === 0) {
        setShowCreateModal(true)
      }
    }

    loadBooks()
  }, [user])

  // Handle cover image selection
  const handleCoverChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      setCoverFile(file)

      // Create preview URL
      const reader = new FileReader()
      reader.onloadend = () => {
        setCoverPreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  // Reset form fields
  const resetForm = () => {
    setNewBookTitle("")
    setNewBookWordGoal("")
    setCoverFile(null)
    setCoverPreview(null)
  }

  // Handle modal close
  const handleCloseModal = () => {
    setShowCreateModal(false)
    resetForm()
  }

  // Create new book
  const handleCreateBook = async () => {
    if (!user) return

    // Validate form
    if (!newBookTitle.trim()) {
      toast({
        variant: "destructive",
        title: "Title required",
        description: "Please enter a title for your book.",
      })
      return
    }

    // Validate word goal (make sure it's a number > 0)
    const wordGoal = parseInt(newBookWordGoal)
    if (isNaN(wordGoal) || wordGoal <= 0) {
      toast({
        variant: "destructive",
        title: "Invalid word goal",
        description: "Please enter a valid target word count.",
      })
      return
    }

    setIsCreating(true)

    try {
      // Upload cover image if provided
      let coverImageUrl = null
      if (coverFile) {
        coverImageUrl = await uploadCoverImage(user.id, coverFile)
      }

      // Create book in database
      const newBook = await createProject(
        newBookTitle,
        user.id,
        wordGoal,
        coverImageUrl
      )

      if (newBook) {
        // Add new book to state
        setBooks([newBook, ...books])

        toast({
          title: "Project created",
          description: `"${newBookTitle}" has been created successfully.`,
        })

        // Close modal and reset form
        handleCloseModal()

        // Create a default empty chapter for the new book
        console.log(`Project ${newBook.id} created. Creating default chapter...`)
        const defaultChapter = await createChapter(
          newBook.id,
          "Chapter 1",
          1, // This parameter is now chapterOrder in the function definition
          ""
        )

        if (!defaultChapter) {
          console.error("Failed to create default chapter for project:", newBook.id)
          // Still redirect to the project, but show a warning
          toast({
            variant: "destructive",
            title: "Warning",
            description: "Project created, but failed to create initial chapter. You may need to create a chapter manually."
          })
        } else {
          console.log(`Default chapter ${defaultChapter.id} created successfully`)
        }

        // Redirect to the writing page for the new book
        router.push(`/project/${newBook.id}`)
      } else {
        throw new Error("Failed to create project")
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Error creating project",
        description: error.message || "An error occurred while creating your project.",
      })
    } finally {
      setIsCreating(false)
    }
  }

  // Get progress data for a specific book
  const getBookProgress = (book: Book) => {
    // Return the progress data from our real-time tracker, or default values if not available yet
    return projectProgress[book.id] || {
      calculated_word_count: 0,
      goal: book.goal || 100000,
      progress_percentage: 0
    }
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Navbar />

      <main className="flex-1 py-16 px-4 md:px-6 mt-16">
        <div className="container max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-8 mb-8">
            <div>
              <h1 className="text-3xl font-bold">Your Projects</h1>
              <p className="text-muted-foreground mt-1">Manage and continue your writing projects</p>
            </div>
          </div>

          {isLoading ? (
            // Loading state - we'll show skeleton loaders matching our card layout
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 sm:gap-6">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex flex-col rounded-md overflow-hidden bg-card border border-border">
                  {/* Image skeleton */}
                  <div className="aspect-[10/16] bg-muted animate-pulse"></div>
                  {/* Info area skeleton */}
                  <div className="p-3">
                    <div className="h-4 bg-muted animate-pulse rounded mb-2 w-3/4"></div>
                    <div className="h-3 bg-muted animate-pulse rounded mb-2 w-1/2"></div>
                    <div className="h-1.5 bg-muted animate-pulse rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : books.length === 0 ? (
            // Empty state
            <div className="premium-card flex flex-col items-center justify-center text-center p-12 rounded-xl">
              <BookOpen className="h-16 w-16 text-muted-foreground mb-4" />
              <h3 className="text-xl font-bold mb-2">No projects yet</h3>
              <p className="text-muted-foreground max-w-md mb-6">
                Start your writing journey by creating your first project. Click the button below to begin.
              </p>
              <Button variant="gold" onClick={() => setShowCreateModal(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Project
              </Button>
            </div>
          ) : (
            // Responsive book grid
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 sm:gap-6">
              {books.map((book) => (
                <Link href={`/project/${book.id}`} key={book.id} className="group block">
                  <div className="flex flex-col rounded-md overflow-hidden shadow-md transition-all duration-200 group-hover:shadow-xl bg-card border border-border">
                    {/* Book cover - Fixed aspect ratio container */}
                    <div className="aspect-[10/16] relative">
                      <div className="absolute inset-0 bg-gradient-to-b from-primary/5 to-primary/20 z-0"></div>

                      {book.cover_image_url ? (
                        // Real cover image with 10:16 aspect ratio and object-fit: cover
                        <div className="relative w-full h-full z-10">
                          <Image
                            src={book.cover_image_url}
                            alt={book.title}
                            fill
                            className="object-cover"
                            sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
                          />
                        </div>
                      ) : (
                        // Generated cover with title
                        <div className="relative flex items-center justify-center w-full h-full z-10 p-4 bg-gradient-to-b from-primary/30 to-primary/10">
                          <h3 className="text-lg sm:text-xl font-serif font-bold text-center">{book.title}</h3>
                        </div>
                      )}
                    </div>

                    {/* Book info area - Separate non-overlapping section */}
                    <div className="p-3 bg-card">
                      <h4 className="font-medium text-foreground mb-1 truncate">{book.title}</h4>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          router.push(`/project/${book.id}/settings`);
                        }}
                        className="inline-flex items-center gap-1 text-xs text-muted-foreground hover:text-primary transition-colors mb-2"
                        style={{ marginBottom: 6, background: "none", border: "none", padding: 0, cursor: "pointer" }}
                      >
                        <Settings className="h-4 w-4" />
                        Project Settings
                      </button>
                      <div className="text-xs text-muted-foreground mb-2">
                        {getBookProgress(book).calculated_word_count.toLocaleString()} / {getBookProgress(book).goal.toLocaleString()} words
                      </div>
                      <GoldProgress value={getBookProgress(book).progress_percentage} className="h-1.5" />
                    </div>
                  </div>
                </Link>
              ))}

              {/* Create New Project Card with enhanced styling */}
              <div
                onClick={() => setShowCreateModal(true)}
                className="cursor-pointer flex flex-col rounded-md border-2 border-dashed border-primary/30 bg-primary/5 transition-all hover:bg-primary/10 hover:border-primary group h-full shadow-sm hover:shadow-md"
              >
                {/* Maintain same aspect ratio as other cards */}
                <div className="aspect-[10/16] flex flex-col items-center justify-center p-4">
                  <div className="h-16 w-16 rounded-full bg-primary/20 flex items-center justify-center mb-4 transition-transform group-hover:scale-110 group-hover:bg-primary/30 shadow-sm">
                    <Plus className="h-8 w-8 text-primary/80 group-hover:text-primary transition-colors" />
                  </div>
                  <h3 className="text-lg font-bold text-center text-primary/80 group-hover:text-primary">Create New Project</h3>
                </div>

                {/* Add footer area to match other cards but with emphasis */}
                <div className="p-3 bg-card border-t border-primary/20">
                  <div className="font-medium text-foreground mb-1">New Project</div>
                  <div className="text-xs text-muted-foreground mb-2">
                    Click to start writing
                  </div>
                  <div className="h-1.5 bg-primary/20 rounded"></div>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>

      <Footer />

      {/* Create Book Modal */}
      <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Create New Project</DialogTitle>
            <DialogDescription>
              Set up your new writing project with a title, target word count, and optional cover image.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-6 py-4">
            <div className="space-y-2">
              <Label htmlFor="book-title">Project Title <span className="text-red-500">*</span></Label>
              <Input
                id="book-title"
                placeholder="Enter project title"
                value={newBookTitle}
                onChange={(e) => setNewBookTitle(e.target.value)}
                className="bg-muted/50"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="word-goal">Target Word Count <span className="text-red-500">*</span></Label>
              <div className="relative">
                <Target className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="word-goal"
                  type="number"
                  placeholder="50000"
                  value={newBookWordGoal}
                  onChange={(e) => setNewBookWordGoal(e.target.value)}
                  className="bg-muted/50 pl-10"
                  min="1"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="cover-image">Cover Image (Optional)</Label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="flex flex-col">
                  <Label
                    htmlFor="cover-upload"
                    className="cursor-pointer border h-40 rounded-md border-dashed border-border flex flex-col items-center justify-center bg-muted/50 hover:bg-muted transition-colors"
                  >
                    <FileImage className="h-8 w-8 text-muted-foreground mb-2" />
                    <span className="text-sm text-muted-foreground">Upload cover image</span>
                    <input
                      id="cover-upload"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={handleCoverChange}
                    />
                  </Label>
                  <span className="text-xs text-muted-foreground mt-1 text-center">
                    Recommended: 10:16 ratio
                  </span>
                </div>

                {coverPreview ? (
                  <div className="aspect-[10/16] relative rounded-md overflow-hidden">
                    <Image
                      src={coverPreview}
                      alt="Cover preview"
                      fill
                      className="object-cover"
                    />
                  </div>
                ) : (
                  <div className="aspect-[10/16] rounded-md bg-muted/50 flex items-center justify-center">
                    <span className="text-sm text-muted-foreground">Preview area</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleCloseModal} disabled={isCreating}>
              Cancel
            </Button>
            <Button variant="gold" onClick={handleCreateBook} disabled={isCreating || !newBookTitle || !newBookWordGoal}>
              {isCreating ? (
                <div className="flex items-center">
                  <div className="h-4 w-4 border-2 border-primary-foreground/50 border-t-primary-foreground rounded-full animate-spin mr-2"></div>
                  Creating...
                </div>
              ) : (
                <>Create Project</>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
