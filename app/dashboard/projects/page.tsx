'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { Loader2, PlusCircle, BookOpen, Trash2 } from 'lucide-react';
import { toast } from 'sonner';

import { supabase } from '@/lib/supabase/client';
import { useAuth } from '@/lib/providers/auth-provider';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { Navbar } from '@/components/navbar';

// Define Project type
type Project = {
  id: string;
  title: string;
  author: string;
  updated_at: string;
};

export default function ProjectsListPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [projectToDelete, setProjectToDelete] = useState<string | null>(null);
  const [confirmText, setConfirmText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const { user } = useAuth();

  // Fetch projects on component mount
  useEffect(() => {
    const fetchProjects = async () => {
      if (!user) return;

      try {
        const { data, error } = await supabase
          .from('projects')
          .select('*')
          .eq('user_id', user.id)
          .order('updated_at', { ascending: false });

        if (error) throw error;
        setProjects(data || []);
      } catch (error: any) {
        toast.error('Failed to load projects');
        console.error('Error loading projects:', error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, [user]);

  const handleDelete = async () => {
    if (!projectToDelete) return;

    setIsDeleting(true);
    try {
      // First delete associated chapters
      const { error: chaptersError } = await supabase
        .from('chapters')
        .delete()
        .eq('project_id', projectToDelete);

      if (chaptersError) throw chaptersError;

      // Then delete the project
      const { error: projectError } = await supabase
        .from('projects')
        .delete()
        .eq('id', projectToDelete);

      if (projectError) throw projectError;

      setProjects(projects.filter(project => project.id !== projectToDelete));
      toast.success('Project deleted successfully');
    } catch (error: any) {
      toast.error('Failed to delete project');
      console.error('Error deleting project:', error.message);
    } finally {
      setIsDeleting(false);
      setProjectToDelete(null);
      setConfirmText('');
    }
  };

  // Format the date for display
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (error) {
      return 'Unknown date';
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      <Navbar />

      <main className="flex-1 py-16 px-4 md:px-6 mt-16">
        <div className="container max-w-6xl mx-auto">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold">Your Projects</h1>
              <p className="text-muted-foreground mt-1">Manage your writing projects</p>
            </div>

            <div className="mt-4 sm:mt-0">
              <Link href="/dashboard/new" passHref>
                <Button>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Create New Project
                </Button>
              </Link>
            </div>
          </div>

          {loading ? (
            // Loading state with skeletons
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="overflow-hidden">
                  <CardHeader className="p-0">
                    <Skeleton className="h-24 rounded-t-lg" />
                  </CardHeader>
                  <CardContent className="p-6">
                    <Skeleton className="h-6 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/2 mb-4" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-1/3" />
                      <Skeleton className="h-4 w-1/4" />
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between p-4">
                    <Skeleton className="h-10 w-20" />
                    <Skeleton className="h-10 w-20" />
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : projects.length === 0 ? (
            // Empty state
            <div className="flex flex-col items-center justify-center text-center p-12 border border-dashed rounded-lg">
              <BookOpen className="h-16 w-16 text-muted-foreground mb-4" />
              <h3 className="text-xl font-bold mb-2">No projects yet</h3>
              <p className="text-muted-foreground max-w-md mb-6">
                Start your writing journey by creating your first project.
              </p>
              <Link href="/dashboard/new" passHref>
                <Button>
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Create Your First Project
                </Button>
              </Link>
            </div>
          ) : (
            // Projects grid
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {projects.map((project) => (
                <Card key={project.id} className="overflow-hidden">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-xl">{project.title}</CardTitle>
                    <CardDescription>{project.author}</CardDescription>
                  </CardHeader>
                  <CardContent className="pb-3">
                    <p className="text-sm text-muted-foreground">
                      Last updated: {formatDate(project.updated_at)}
                    </p>
                  </CardContent>
                  <CardFooter className="flex justify-between pt-3">
                    <Link href={`/book/${project.id}/edit`} passHref>
                      <Button>Open</Button>
                    </Link>
                    <Button 
                      variant="destructive" 
                      onClick={() => setProjectToDelete(project.id)}
                    >
                      Delete
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </div>
      </main>

      {/* Delete Confirmation Dialog */}
      <AlertDialog 
        open={!!projectToDelete} 
        onOpenChange={(open) => {
          if (!open) {
            setProjectToDelete(null);
            setConfirmText('');
          }
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete your project and all its chapters.
              <div className="mt-4">
                <p className="mb-2 text-sm font-medium">Type DELETE to confirm:</p>
                <Input 
                  value={confirmText} 
                  onChange={(e) => setConfirmText(e.target.value)} 
                  className="mt-1"
                  disabled={isDeleting}
                  placeholder="DELETE"
                />
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                if (confirmText === 'DELETE') {
                  handleDelete();
                } else {
                  toast.error('Please type DELETE to confirm');
                }
              }}
              disabled={confirmText !== 'DELETE' || isDeleting}
              className="bg-destructive text-destructive-foreground"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Project
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}