// app/layout.tsx — Server component; imports your Tailwind/UI globals and wraps every page
import '@/app/globals.css'        // Importing from app/globals.css for styling
import React from 'react'
import SupabaseProvider from '@/components/SupabaseProvider'
import { AuthProvider } from '@/lib/providers/auth-provider'

export const metadata = {
  title: 'BookWriter',
  description: 'The ultimate AI-powered writing platform',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark" style={{ colorScheme: 'dark' }}>
      <body className="font-sans">
        <SupabaseProvider>
          <AuthProvider>
            {children}
          </AuthProvider>
        </SupabaseProvider>
      </body>
    </html>
  )
}
