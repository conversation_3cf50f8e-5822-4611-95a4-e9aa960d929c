import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"

export default function TermsPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Navbar />

      <main className="flex-1 py-16 px-4 md:px-6 mt-16">
        <div className="container max-w-3xl mx-auto">
          <div className="mb-10">
            <h1 className="text-3xl font-bold">Terms of Service</h1>
            <p className="text-muted-foreground mt-2">Last updated: April 29, 2023</p>
          </div>

          <div className="prose prose-invert max-w-none">
            <p>
              Welcome to BookWriter. These Terms of Service govern your use of our website, services, and applications
              (collectively, the "Service"). By using the Service, you agree to these Terms.
            </p>

            <h2>1. Acceptance of Terms</h2>
            <p>
              By accessing or using the Service, you acknowledge that you have read, understood, and agree to be bound
              by these Terms. If you do not agree, you may not use the Service.
            </p>

            <h2>2. Description of Service</h2>
            <p>
              BookWriter provides a writing platform for authors to create, edit, and manage their books. We offer
              various features including a writing interface, soundscapes, export options, and more.
            </p>

            <h2>3. Account Registration and Security</h2>
            <p>
              To use certain features of the Service, you must register for an account. You are responsible for
              maintaining the confidentiality of your account information and for all activities that occur under your
              account.
            </p>

            <h2>4. User Content and Ownership</h2>
            <p>
              You retain all rights to the content you create using our Service. We do not claim ownership over your
              books, stories, or other content. You are responsible for the content you create.
            </p>

            <h2>5. Subscription and Payments</h2>
            <p>
              BookWriter offers both free and paid subscription plans. By subscribing to a paid plan, you agree to pay
              the fees associated with your chosen plan. We may change our fees at any time, but will provide notice
              before changes take effect.
            </p>

            <h2>6. Cancellation and Refunds</h2>
            <p>
              You can cancel your subscription at any time. Upon cancellation, your account will remain active until the
              end of your current billing period. We do not provide refunds for partial subscription periods.
            </p>

            <h2>7. Termination</h2>
            <p>
              We reserve the right to terminate or suspend your account at our discretion, without notice, for conduct
              that we believe violates these Terms or is harmful to other users, us, or third parties, or for any other
              reason.
            </p>

            <h2>8. Limitation of Liability</h2>
            <p>
              To the maximum extent permitted by law, BookWriter shall not be liable for any indirect, incidental,
              special, consequential, or punitive damages, including without limitation, loss of profits, data, or other
              intangible losses.
            </p>

            <h2>9. Changes to Terms</h2>
            <p>
              We may modify these Terms at any time. If we make material changes, we will notify you via email or
              through the Service. Your continued use of the Service after such notification constitutes your acceptance
              of the new Terms.
            </p>

            <h2>10. Governing Law</h2>
            <p>
              These Terms shall be governed by the laws of the jurisdiction in which we operate, without regard to its
              conflict of law provisions.
            </p>

            <h2>Contact Us</h2>
            <p>If you have any questions about these Terms, please contact <NAME_EMAIL>.</p>
          </div>

          <div className="mt-10 flex justify-center">
            <Link href="/" passHref>
              <Button variant="outline">Back to Home</Button>
            </Link>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
