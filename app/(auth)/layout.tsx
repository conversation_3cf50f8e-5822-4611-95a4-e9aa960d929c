// app/(auth)/layout.tsx
import React from 'react'
import Su<PERSON>baseProvider from '@/components/SupabaseProvider'
import { AuthProvider } from '@/lib/providers/auth-provider'

export const metadata = {
  title: 'BookWriter - Login',
  description: 'The ultimate AI‐powered writing platform',
}

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="bg-[#0F0F0F] min-h-screen flex items-center justify-center">
      {children}
    </div>
  )
}
