import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { Database } from '@/lib/supabase/database.types';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const origin = requestUrl.origin;

  if (code) {
    const supabase = createRouteHandlerClient<Database>({ cookies: () => cookies() });

    try {
      // Exchange the code for a session
      const { error } = await supabase.auth.exchangeCodeForSession(code);
      
      if (error) {
        console.error("[auth/callback] Error exchanging code for session:", error.message);
        // Redirect to login with error
        return NextResponse.redirect(`${origin}/login?error=${encodeURIComponent(error.message)}`);
      }
      
      console.log("[auth/callback] Successfully exchanged code for session");
    } catch (err: any) {
      console.error("[auth/callback] Unexpected error:", err.message);
      return NextResponse.redirect(`${origin}/login?error=An unexpected error occurred`);
    }
  } else {
    console.error("[auth/callback] No code provided in URL");
    return NextResponse.redirect(`${origin}/login?error=Authentication failed`);
  }

  // Redirect to the dashboard
  return NextResponse.redirect(`${origin}/dashboard`);
}
