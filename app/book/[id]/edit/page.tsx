"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { EditorSidebar } from "@/components/sidebar"
import { EditorToolbar } from "@/components/editor-toolbar"
import { SoundscapePlayer } from "@/components/soundscape-player"
import { Navbar } from "@/components/navbar"
import { useToast } from "@/hooks/use-toast"
import { Download, EyeIcon, Lock } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { UpgradeButton } from "@/components/upgrade-button"

export default function BookEditorPage() {
  const params = useParams()
  const bookId = params.id as string
  const [content, setContent] = useState<string>(
    "The soft glow of the screen illuminated her face as she began to type. Words flowed effortlessly from her fingertips, painting vivid scenes and breathing life into characters that had only existed in her mind moments before.\n\nShe paused, considering the direction of her narrative. The protagonist stood at a crossroads, both literally and figuratively. Which path would lead to the heart of the story she was trying to tell?\n\nThe coffee beside her had grown cold, forgotten in the rush of creation. Outside, the world continued its chaotic dance, but here, in this moment, everything made perfect sense. She smiled and continued writing, losing herself in the world of her own making.",
  )
  const [wordCount, setWordCount] = useState(0)
  const [theme, setTheme] = useState<"dark" | "light" | "sepia" | "soft-gray">("dark")
  const { toast } = useToast()

  useEffect(() => {
    // Calculate word count
    const words = content.trim().split(/\s+/).filter(Boolean).length
    setWordCount(words)
  }, [content])

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setContent(e.target.value)
  }

  const changeTheme = (newTheme: "dark" | "light" | "sepia" | "soft-gray") => {
    setTheme(newTheme)
    toast({
      title: "Theme Changed",
      description: `Editor theme set to ${newTheme}.`,
    })
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Navbar />

      <main className="flex-1 mt-16">
        <EditorToolbar />
        <EditorSidebar bookId={bookId} />

        <div className="ml-64 flex flex-col h-[calc(100vh-4rem)]">
          <div
            className={`flex-1 overflow-auto transition-colors ${
              theme === "light"
                ? "bg-white text-black"
                : theme === "sepia"
                  ? "bg-[#f8f1e3] text-[#5f4b32]"
                  : theme === "soft-gray"
                    ? "bg-[#2a2a2a] text-[#e0e0e0]"
                    : "bg-background text-foreground"
            }`}
          >
            <div className="max-w-3xl mx-auto px-6 py-12">
              <textarea
                value={content}
                onChange={handleContentChange}
                className={`w-full h-full min-h-[70vh] resize-none bg-transparent border-none outline-none focus:ring-0 text-lg font-serif leading-relaxed ${
                  theme === "light"
                    ? "placeholder:text-gray-400"
                    : theme === "sepia"
                      ? "placeholder:text-[#9e8a6d]"
                      : "placeholder:text-muted-foreground"
                }`}
                placeholder="Begin your masterpiece..."
              />
            </div>
          </div>

          <div
            className={`border-t flex justify-between items-center py-2 px-4 ${
              theme === "light"
                ? "bg-gray-100 border-gray-200 text-gray-700"
                : theme === "sepia"
                  ? "bg-[#e8e0d0] border-[#d8ccb7] text-[#5f4b32]"
                  : "bg-muted border-border text-muted-foreground"
            }`}
          >
            <div className="flex space-x-2">
              <Select value={theme} onValueChange={(value) => changeTheme(value as any)}>
                <SelectTrigger className="h-8 text-xs w-36">
                  <SelectValue placeholder="Select theme" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="dark">Black/White</SelectItem>
                  <SelectItem value="light">White/Black</SelectItem>
                  <SelectItem value="soft-gray">Soft Gray/Black</SelectItem>
                  <SelectItem value="sepia">Sepia/Classic Cream</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm">{wordCount.toLocaleString()} words</span>
              <div className="h-4 border-l border-border mx-2"></div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="flex items-center h-8">
                    <Download className="h-3.5 w-3.5 mr-1" /> Export
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={() =>
                      toast({
                        title: "Export Started",
                        description: "Your book is being exported to PDF.",
                      })
                    }
                  >
                    Export to PDF
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() =>
                      toast({
                        variant: "destructive",
                        title: "Upgrade Required",
                        description: "Please upgrade to Pencil Sharpener to unlock EPUB exports.",
                      })
                    }
                    className="text-muted-foreground"
                  >
                    Export to EPUB <Lock className="h-3 w-3 ml-1" />
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() =>
                      toast({
                        variant: "destructive",
                        title: "Upgrade Required",
                        description: "Please upgrade to Pencil Sharpener to unlock DOCX exports.",
                      })
                    }
                    className="text-muted-foreground"
                  >
                    Export to DOCX <Lock className="h-3 w-3 ml-1" />
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <Link href={`/book/${bookId}/preview`} passHref>
                <Button
                  variant="gold"
                  size="sm"
                  className="flex items-center h-8 shadow-[0_0_10px_rgba(255,215,0,0.3)] hover:shadow-[0_0_15px_rgba(255,215,0,0.5)]"
                >
                  <EyeIcon className="h-3.5 w-3.5 mr-1" /> Preview
                </Button>
              </Link>
            </div>
          </div>
        </div>

        <SoundscapePlayer />
        <UpgradeButton />
      </main>
    </div>
  )
}
