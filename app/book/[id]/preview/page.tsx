"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Navbar } from "@/components/navbar"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ChevronLeft, ChevronRight, Edit, Download } from "lucide-react"

const sampleChapterContent = [
  {
    title: "Chapter 1: The Beginning",
    content: `
      The soft glow of the screen illuminated her face as she began to type. Words flowed effortlessly from her fingertips, painting vivid scenes and breathing life into characters that had only existed in her mind moments before.

      She paused, considering the direction of her narrative. The protagonist stood at a crossroads, both literally and figuratively. Which path would lead to the heart of the story she was trying to tell?

      The coffee beside her had grown cold, forgotten in the rush of creation. Outside, the world continued its chaotic dance, but here, in this moment, everything made perfect sense. She smiled and continued writing, losing herself in the world of her own making.

      Hours passed unnoticed as chapters formed and characters evolved. Dialog sharpened, settings became more vivid, and the plot wove itself into an intricate tapestry. This was why she wrote—to discover the story as it revealed itself to her.

      When she finally looked up, the morning sun was streaming through the window. She had written through the night, and yet she felt more awake than she had in months. The first draft was complete. Now the real work could begin.
    `,
  },
  {
    title: "Chapter 2: The Journey",
    content: `
      The road stretched out before them, a ribbon of possibility cutting through the rolling hills. They had been traveling for three days now, and the familiar comfort of home had faded into a distant memory.

      "How much further?" asked the younger of the two, adjusting the weight of the pack on his shoulders. His companion, a weathered woman with eyes that had seen too much, consulted the worn map in her hands.

      "Another day to the river crossing, then two more to the mountains. If we're lucky with the weather." She folded the map carefully, tucking it into an inner pocket. "We should make camp soon. There's a clearing just ahead."

      As they set up their modest camp, the last rays of sunlight painted the sky in hues of gold and crimson. The silence between them was comfortable, born of years of shared experiences and mutual understanding.

      Later, as they sat by the fire, the younger traveler broke the silence. "Do you think we'll find what we're looking for?"

      The woman stared into the flames, her face a mask of determination. "We have to," she replied simply. "There's no going back now."
    `,
  },
  {
    title: "Chapter 3: The Encounter",
    content: `
      The mist clung to the forest floor, weaving between ancient trees like ghostly serpents. The air was thick with the scent of damp earth and decaying leaves. They moved cautiously, each step calculated to minimize sound.

      "There," whispered the guide, pointing to a barely visible path that veered to the right. "That's where we'll find it."

      The younger traveler hesitated, a chill running down his spine that had nothing to do with the morning air. "Are you sure this is wise? The stories—"

      "Are just stories," the woman interrupted, though her hand instinctively moved to the hilt of her blade. "Until proven otherwise."

      They followed the path for what seemed like hours, the forest growing denser, the light dimmer. Then, without warning, they emerged into a clearing. In its center stood a structure unlike anything they had ever seen—ancient yet somehow untouched by time.

      "The Temple of Whispers," breathed the guide, awe evident in her voice. "We found it."

      As if in response to her words, the massive stone doors of the temple began to open, revealing nothing but darkness within. And from that darkness came a voice, neither male nor female, neither young nor old.

      "We have been waiting for you."
    `,
  },
]

export default function BookPreviewPage() {
  const params = useParams()
  const router = useRouter()
  const bookId = params.id as string
  const [currentPage, setCurrentPage] = useState(0)
  const [currentChapter, setCurrentChapter] = useState(0)

  // Calculate total pages based on content length
  const pagesPerChapter = sampleChapterContent.map((chapter) =>
    Math.ceil(chapter.content.split("\n\n").filter((p) => p.trim()).length / 2),
  )

  const totalPages = pagesPerChapter[currentChapter] || 10

  const previousPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1)
    } else if (currentChapter > 0) {
      setCurrentChapter(currentChapter - 1)
      setCurrentPage(pagesPerChapter[currentChapter - 1] - 1)
    }
  }

  const nextPage = () => {
    if (currentPage < totalPages - 1) {
      setCurrentPage(currentPage + 1)
    } else if (currentChapter < sampleChapterContent.length - 1) {
      setCurrentChapter(currentChapter + 1)
      setCurrentPage(0)
    }
  }

  const handleChapterChange = (index: number) => {
    setCurrentChapter(index)
    setCurrentPage(0)
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Navbar />

      <main className="flex-1 mt-16 bg-[#f8f1e3] text-[#5f4b32]">
        <div className="fixed top-16 left-0 right-0 bg-[#f8f1e3] border-b border-[#d8ccb7] py-2 px-4 flex flex-col z-20">
          <div className="flex justify-between items-center mb-2">
            <Link href={`/book/${bookId}/edit`} passHref>
              <Button variant="outline" size="sm" className="bg-white border-[#d8ccb7] text-[#5f4b32]">
                <Edit className="h-4 w-4 mr-2" /> Edit Book
              </Button>
            </Link>

            <div className="text-sm font-medium">
              {sampleChapterContent[currentChapter].title} • Page {currentPage + 1} of {totalPages}
            </div>

            <Button variant="outline" size="sm" className="bg-white border-[#d8ccb7] text-[#5f4b32]">
              <Download className="h-4 w-4 mr-2" /> Export
            </Button>
          </div>

          <Tabs
            value={currentChapter.toString()}
            onValueChange={(value) => handleChapterChange(Number.parseInt(value))}
            className="w-full"
          >
            <TabsList className="bg-[#e8e0d0] w-full justify-start overflow-x-auto">
              {sampleChapterContent.map((chapter, index) => (
                <TabsTrigger
                  key={index}
                  value={index.toString()}
                  className="data-[state=active]:bg-white data-[state=active]:text-[#5f4b32]"
                >
                  Chapter {index + 1}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </div>

        <div className="pt-28 pb-16 px-4 min-h-[calc(100vh-4rem)]">
          <div className="max-w-2xl mx-auto my-8 px-8 py-12 bg-white shadow-lg rounded-sm min-h-[80vh] font-serif">
            {currentPage === 0 && (
              <h2 className="text-2xl font-bold mb-8 text-center">{sampleChapterContent[currentChapter].title}</h2>
            )}

            <div className="text-lg leading-relaxed">
              {sampleChapterContent[currentChapter].content
                .split("\n\n")
                .filter((p) => p.trim())
                .slice(currentPage * 2, currentPage * 2 + 2)
                .map((paragraph, i) => (
                  <p key={i} className="mb-8 first-letter:text-xl first-letter:font-bold">
                    {paragraph.trim()}
                  </p>
                ))}
            </div>
          </div>
        </div>

        <div className="fixed bottom-0 left-0 right-0 bg-[#f8f1e3] border-t border-[#d8ccb7] py-3 px-6 flex justify-between items-center">
          <Button
            variant="outline"
            size="sm"
            onClick={previousPage}
            disabled={currentChapter === 0 && currentPage === 0}
            className="bg-white border-[#d8ccb7] text-[#5f4b32]"
          >
            <ChevronLeft className="h-4 w-4 mr-1" /> Previous
          </Button>

          <div className="text-sm font-medium">
            Page {currentPage + 1} of {totalPages}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={nextPage}
            disabled={currentChapter === sampleChapterContent.length - 1 && currentPage === totalPages - 1}
            className="bg-white border-[#d8ccb7] text-[#5f4b32]"
          >
            Next <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      </main>
    </div>
  )
}
