import { NextRequest, NextResponse } from 'next/server';
import { Database } from '@/lib/supabase/database.types';
import { authenticateApiRequest } from '@/lib/supabase/auth-utils';

import { createOrUpdateBookMemory } from '@/lib/supabase/database';
import { htmlToMarkdown } from '@/lib/supabase/markdown-conversion';
import { callOpenAI, generateBookMemoryPrompt } from '@/lib/supabase/openai';

/**
 * BookMemory™ API endpoint
 * Generates and stores summaries of chapters for maintaining story consistency
 * Only available to paid users
 */
export async function POST(request: NextRequest) {
  try {
    console.log('[book-memory] API route called - Using JWT auth');
    
    // Authenticate the request
    const { user, supabase, authError, handleAuthError } = await authenticateApiRequest(request);
    
    // Check if the user is authenticated
    const authErrorResponse = handleAuthError();
    if (authErrorResponse) {
      return authErrorResponse;
    }
    
    // Ensure user is not null after authentication
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request data
    const { htmlContent, projectId, chapterId } = await request.json();

    // Validate request data
    if (!htmlContent || !projectId || !chapterId) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
    }
    
    // Verify the user has access to this project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single();
    
    if (projectError || !project) {
      return NextResponse.json({ error: 'Project not found or access denied' }, { status: 403 });
    }
    
    // Check if the chapter belongs to the project
    const { data: chapter, error: chapterError } = await supabase
      .from('chapters')
      .select('*')
      .eq('id', chapterId)
      .eq('project_id', projectId)
      .single();
    
    if (chapterError || !chapter) {
      return NextResponse.json({ error: 'Chapter not found or does not belong to this project' }, { status: 403 });
    }
    
    // Check if user is on the paid plan
    // This should be replaced with actual subscription detection
    const { data: subscription } = await supabase
      .from('subscriptions') // Assuming you have a subscriptions table
      .select('*')
      .eq('user_id', user.id)
      .is('canceled_at', null)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();
    
    const isPaidUser = subscription !== null;
    
    if (!isPaidUser) {
      return NextResponse.json({ 
        error: 'BookMemory™ is a premium feature. Please upgrade to Pencil Sharpener plan.',
      }, { status: 403 });
    }
    
    // Convert HTML to Markdown
    const markdownContent = htmlToMarkdown(htmlContent);
    
    // Generate the BookMemory™ prompt
    const prompt = generateBookMemoryPrompt(markdownContent);
    
    // Call OpenAI API to generate the summary
    const { text: summary, tokensUsed } = await callOpenAI(prompt);
    
    // Store the summary in the database
    const bookMemory = await createOrUpdateBookMemory(
      projectId,
      chapterId,
      summary
    );
    
    if (!bookMemory) {
      throw new Error('Failed to store BookMemory™ summary');
    }
    
    // Return the response
    return NextResponse.json({
      summary,
      tokensUsed,
      id: bookMemory.id,
      created_at: bookMemory.created_at
    });
    
  } catch (error: any) {
    console.error('Error processing BookMemory™ request:', error);
    return NextResponse.json({ error: error.message || 'An error occurred' }, { status: 500 });
  }
}

/**
 * Get all BookMemory™ entries for a project
 */
export async function GET(request: NextRequest) {
  try {
    console.log('[book-memory-GET] API route called - Using JWT auth');
    
    // Authenticate the request
    const { user, supabase, authError, handleAuthError } = await authenticateApiRequest(request);
    
    // Check if the user is authenticated
    const authErrorResponse = handleAuthError();
    if (authErrorResponse) {
      return authErrorResponse;
    }
    
    // Ensure user is not null after authentication
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get project ID from query parameters
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    if (!projectId) {
      return NextResponse.json({ error: 'Missing projectId parameter' }, { status: 400 });
    }
    
    // Verify the user has access to this project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single();
    
    if (projectError || !project) {
      return NextResponse.json({ error: 'Project not found or access denied' }, { status: 403 });
    }
    
    // Check if user is on the paid plan
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .is('canceled_at', null)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();
    
    const isPaidUser = subscription !== null;
    
    if (!isPaidUser) {
      return NextResponse.json({ 
        error: 'BookMemory™ is a premium feature. Please upgrade to Pencil Sharpener plan.', 
      }, { status: 403 });
    }
    
    // Fetch all BookMemory™ entries for this project
    const { data: memories, error: memoriesError } = await supabase
      .from('book_memory')
      .select('*, chapters:chapter_id(title, chapter_order)')
      .eq('project_id', projectId)
      .order('chapters.chapter_order', { ascending: true });
    
    if (memoriesError) {
      throw new Error(`Failed to fetch BookMemory™ entries: ${memoriesError.message}`);
    }
    
    return NextResponse.json({ memories: memories || [] });
    
  } catch (error: any) {
    console.error('Error fetching BookMemory™ entries:', error);
    return NextResponse.json({ error: error.message || 'An error occurred' }, { status: 500 });
  }
}
