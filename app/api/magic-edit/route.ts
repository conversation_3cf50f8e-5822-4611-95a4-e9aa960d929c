import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';
import { OpenAI } from 'openai';
import { authenticateApiRequest } from '@/lib/supabase/api-auth';
import * as Diff from 'diff'; // Import the diff library

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  project: process.env.OPENAI_PROJECT_ID, // Add project ID for project key authentication
});

// Log configuration for debugging
console.log('OpenAI API Config:', {
  hasApiKey: !!process.env.OPENAI_API_KEY,
  keyType: process.env.OPENAI_API_KEY?.startsWith('sk-proj-') ? 'Project Key' : 'Secret Key',
  hasProjectId: !!process.env.OPENAI_PROJECT_ID
});

// Magic Edit limits per plan
const MAGIC_EDIT_LIMITS = {
  free: 5,   // 5 Magic Edits per month for free users
  paid: 25   // 25 Magic Edits per month for paid users
};

export async function POST(request: NextRequest) {
  try {
    // Authenticate the request
    const { user, supabase, authError } = await authenticateApiRequest(request);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized: Please sign in to use Magic Edit™' },
        { status: 401 }
      );
    }
    
    const userId = user.id;
    
    // Parse request body
    const body = await request.json();
    console.log('Magic Edit API received body:', body);
    const { projectId, chapterId, htmlContent } = body;

    if (!projectId || !chapterId || !htmlContent) {
      console.error('Magic Edit API missing params:', { projectId, chapterId, htmlContent });
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }
    
    // Validate that user has access to this project
    console.log('Magic Edit API userId:', userId, typeof userId, userId.length, 'projectId:', projectId, typeof projectId, projectId.length);

    // Try to log the current Supabase session user (if possible)
    if (supabase && typeof supabase.auth?.getUser === 'function') {
      try {
        const sessionUser = await supabase.auth.getUser();
        console.log('Magic Edit API supabase.auth.getUser():', sessionUser);
      } catch (e) {
        console.log('Magic Edit API supabase.auth.getUser() error:', e);
      }
    }

    if (!supabase) {
      console.error('Magic Edit API: Supabase client is not available after authentication.');
      return NextResponse.json(
        { error: 'Internal server error: Could not connect to database services.' },
        { status: 500 }
      );
    }

    const { data: projectData, error: projectError } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .eq('user_id', userId)
      .single();
    console.log('Magic Edit API projectData:', projectData, 'projectError:', projectError);

    if (projectError || !projectData) {
      console.error('Magic Edit API access denied:', { userId, projectId, projectError, projectData });
      return NextResponse.json(
        { error: 'Access denied: You don\'t have permission to edit this project' },
        { status: 403 }
      );
    }
    
    // Check subscription status
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('id')
      .eq('user_id', userId)
      .is('canceled_at', null)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();
    
    const isPaidUser = subscription !== null;
    const magicEditLimit = isPaidUser ? MAGIC_EDIT_LIMITS.paid : MAGIC_EDIT_LIMITS.free;
    
    // Calculate Magic Edits used this month
    const currentMonth = new Date();
    currentMonth.setDate(1); // First day of current month
    currentMonth.setHours(0, 0, 0, 0);
    
    const { count: magicEditsUsed, error: countError } = await supabase
      .from('magic_edits')
      .select('id', { count: 'exact' })
      .eq('user_id', userId)
      .gte('created_at', currentMonth.toISOString());
    
    if (countError) {
      console.error('Error counting magic edits:', countError);
      return NextResponse.json(
        { error: 'Failed to verify Magic Edit™ credits' },
        { status: 500 }
      );
    }
    
    // Extract plain text from HTML for word count validation
    const textContent = extractTextFromHtml(htmlContent);
    const wordCount = countWords(textContent);
    
    // Calculate credits required based on word count (1 credit per 5,000 words)
    const creditsRequired = Math.ceil(wordCount / 5000);
    
    // Limit to reasonable size (50,000 words = 10 credits max)
    if (wordCount > 50000) {
      return NextResponse.json(
        { error: 'Text exceeds the 50,000 word limit for Magic Edit™' },
        { status: 400 }
      );
    }
    
    const editsUsed = magicEditsUsed || 0;
    const editsRemaining = Math.max(0, magicEditLimit - editsUsed);
    
    if (editsRemaining < creditsRequired) {
      return NextResponse.json(
        { 
          error: `This edit requires ${creditsRequired} credits but you only have ${editsRemaining} remaining. Please upgrade to get more credits or edit a smaller section.`,
          creditsRequired,
          creditsRemaining: editsRemaining
        },
        { status: 403 }
      );
    }
    
    // Calculate approximate tokens for later recording (rough estimate)
    const estimatedTokens = wordCount * 1.3; // Average ratio of tokens to words
    
    const apiKey = process.env.OPENAI_API_KEY;
    const endpoint = 'https://api.openai.com/v1';
    const model = 'gpt-4o-2024-08-06';

    const url = `${endpoint}/chat/completions`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: model,
        temperature: 0.2,
        max_tokens: 4000, // Increased max_tokens
        messages: [
          {
            role: 'system',
            content: `You are an expert editor. Please edit the following text to improve grammar, clarity, and flow while maintaining the author's voice and style.

IMPORTANT FORMATTING REQUIREMENTS:
- Return properly formatted HTML with appropriate paragraph tags
- Maintain natural paragraph spacing (not excessive line breaks)
- Keep dialogue and narrative flowing naturally
- Do not add unnecessary empty paragraphs or extra spacing
- Ensure the text reads smoothly when displayed in a web interface

Text to edit:
${htmlContent}

Return only the improved HTML content with proper formatting.`
          },
          {
            role: 'user',
            content: `Here is the chapter to edit:\n\n${htmlContent}`
          }
        ]
      })
    });
    
    if (!response.ok) {
      let errorText = await response.text();
      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { raw: errorText };
      }
      // Print error to both console.error and console.log for maximum visibility
      const errorLog = {
        status: response.status,
        url,
        errorData,
        headers: Object.fromEntries(response.headers.entries()),
        usedKey: apiKey?.slice(0, 12) + '...' // Do not log full key
      };
      if (response.status === 403) {
        console.error('OpenAI API 403 error details:', errorLog);
        console.log('OpenAI API 403 error details:', errorLog);
      } else {
        console.error('OpenAI API error details:', errorLog);
        console.log('OpenAI API error details:', errorLog);
      }
      throw new Error(`API request failed with status ${response.status}: ${JSON.stringify(errorData)}`);
    }
    
    const responseData = await response.json();
    console.log('API call succeeded:', { status: response.status, model: responseData.model || model });
    
    // DETAILED LOGGING START
    console.log('OpenAI responseData:', JSON.stringify(responseData, null, 2)); // Log the full response data

    let editedContent = responseData.choices?.[0]?.message?.content;
    // Clean up AI output
    editedContent = editedContent
      .replace(/<p>\s*<\/p>/g, '') // Remove empty paragraphs
      .replace(/\n\s*\n\s*\n/g, '\n\n') // Reduce multiple newlines
      .replace(/(<\/p>)\s*(<p>)/g, '$1\n\n$2'); // Ensure paragraph spacing
    console.log('Extracted editedContent:', editedContent ? editedContent.substring(0, 200) + "..." : "null");

    if (!editedContent) {
      console.error('editedContent is null or undefined after OpenAI call.');
      return NextResponse.json(
        { error: 'AI failed to return content. Please try again.' },
        { status: 500 }
      );
    }

    // Word-based validation with more lenient thresholds
    const originalPlainText = extractTextFromHtml(htmlContent);
    const editedPlainText = extractTextFromHtml(editedContent);

    const wordDiff = Diff.diffWords(originalPlainText, editedPlainText);
    let addedWords = 0;
    let removedWords = 0;
    
    wordDiff.forEach(part => {
      const words = (part.value.match(/\S+/g) || []).length;
      if (part.added) addedWords += words;
      if (part.removed) removedWords += words;
    });

    console.log('Word diff analysis:', {
      originalWords: (originalPlainText.match(/\S+/g) || []).length,
      editedWords: (editedPlainText.match(/\S+/g) || []).length,
      addedWords,
      removedWords,
    });

    // Updated validation thresholds
    const significantlyTruncated = editedPlainText.length < originalPlainText.length * 0.4; // 40% of original length
    const excessiveNetRemoval = (removedWords - addedWords) > ((originalPlainText.match(/\S+/g) || []).length * 0.5);

    if (significantlyTruncated || excessiveNetRemoval) {
      console.error('Validation failed: AI returned content that was too different or truncated.', {
        significantlyTruncated,
        excessiveNetRemoval,
        originalWords: (originalPlainText.match(/\S+/g) || []).length,
        editedWords: (editedPlainText.match(/\S+/g) || []).length,
        removedWords,
        addedWords
      });
      return NextResponse.json(
        { error: 'AI edit was too drastic or truncated the content. Please try a smaller selection or rephrase.' },
        { status: 400 } // Bad request, as the result is not usable
      );
    }
    // DETAILED LOGGING END (console.logs for diff analysis serve as detailed logging now)
    
    // Record Magic Edit usage in the table - insert multiple records for multi-credit edits
    const timestamp = new Date().toISOString();
    const magicEditRecords = Array.from({ length: creditsRequired }, () => ({
      user_id: userId,
      project_id: projectId,
      chapter_id: chapterId,
      word_count: Math.ceil(wordCount / creditsRequired), // Distribute word count across records
      tokens_used: Math.round(estimatedTokens / creditsRequired), // Distribute tokens across records
      created_at: timestamp
    }));

    const { error: logError } = await supabase
      .from('magic_edits')
      .insert(magicEditRecords);
      
    if (logError) {
      console.error('Failed to log magic edit usage:', logError);
      // Continue anyway as the edit was successful
    }
    
    // Return the edited content with updated credits info
    const remainingCredits = editsRemaining - creditsRequired;
    
    return NextResponse.json({ 
      html: editedContent,
      wordCount: countWords(extractTextFromHtml(editedContent)),
      creditsRemaining: remainingCredits,
      creditsTotal: magicEditLimit,
      creditsUsed: creditsRequired,
      inputWordCount: wordCount
    });
    
  } catch (error: any) {
    console.error('Magic Edit API error details:', {
      message: error.message,
      type: error.type,
      code: error.code,
      param: error.param,
      status: error.status,
      stack: error.stack
    });
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// Helper function to extract text from HTML
function extractTextFromHtml(html: string): string {
  // Server-side approach since we can't use the DOM directly
  return html
    .replace(/<[^>]*>/g, ' ') // Replace HTML tags with spaces
    .replace(/&nbsp;/g, ' ')  // Replace &nbsp; with spaces
    .replace(/\s+/g, ' ')     // Replace multiple spaces with single space
    .trim();
}

// Helper function to count words in text
function countWords(text: string): number {
  return text.split(/\s+/).filter(Boolean).length;
}
