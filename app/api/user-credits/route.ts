// app/api/user-credits/route.ts
import { NextResponse, NextRequest } from 'next/server'
import { Database } from '@/lib/supabase/database.types'
import { getMonthlyMagicEditsCount } from '@/lib/supabase/database'
import { authenticateApiRequest } from '@/lib/supabase/api-auth'

// Magic Edit limits per plan
const MAGIC_EDIT_LIMITS = {
  free: 5,   // 5 Magic Edits per month for free users
  paid: 25   // 25 Magic Edits per month for paid users
}

// Simple in-memory cache to prevent excessive API calls
const cache = {
  data: null as any,
  timestamp: 0
};

// Cache TTL: 5 minutes (300,000ms)
const CACHE_TTL = 300000;

// IMPROVED RATE LIMITING
// Track requests by IP to prevent excessive API calls
const requestLog: Record<string, number[]> = {};
const MAX_REQUESTS_PER_WINDOW = 10; // Increased from 5 to 10 requests per window
const TIME_WINDOW_MS = 10000; // Increased from 5s to 10s window

// Adapted for development environments
const isDevelopment = process.env.NODE_ENV === 'development';

export async function GET(request: NextRequest) {
  console.log('[user-credits] GET request received');

  // IMPROVED RATE LIMITING
  // Get client IP (or a fallback if not available)
  const ip = request.headers.get('x-forwarded-for') ||
             request.headers.get('x-real-ip') ||
             'unknown-ip';

  // Special handling for development environments and localhost
  const isLocalhost = ip === '127.0.0.1' || ip === '::1' || ip.startsWith('192.168.');
  const effectiveMaxRequests = isDevelopment || isLocalhost 
    ? MAX_REQUESTS_PER_WINDOW * 10 // Much more permissive for development
    : MAX_REQUESTS_PER_WINDOW;

  const key = `${ip}:user-credits`;
  const now = Date.now();

  // Initialize request log for this client if it doesn't exist
  if (!requestLog[key]) {
    requestLog[key] = [];
  }

  // Filter out requests older than the time window
  // Only keep track of the last 100 requests to prevent memory leaks
  requestLog[key] = requestLog[key]
    .filter(timestamp => now - timestamp < TIME_WINDOW_MS)
    .slice(-100);
    
  // Clean up the request log periodically to prevent memory leaks
  // Do this every 100 requests
  if (Math.random() < 0.01) { // 1% chance on each request
    console.log('[user-credits] Cleaning up request log');
    for (const key in requestLog) {
      // Remove entries older than 1 hour
      requestLog[key] = requestLog[key].filter(timestamp => now - timestamp < 3600000);
      // If no entries left, remove the key
      if (requestLog[key].length === 0) {
        delete requestLog[key];
      }
    }
  }

  // Calculate remaining requests for this window
  const requestsMade = requestLog[key].length;
  const remainingRequests = Math.max(0, effectiveMaxRequests - requestsMade);

  // Check if client has made too many requests
  if (requestsMade >= effectiveMaxRequests) {
    console.warn(`[RATE-LIMITER] Blocking excessive requests from ${ip} to user-credits API (${requestsMade}/${effectiveMaxRequests} in ${TIME_WINDOW_MS/1000}s window)`);

    // Always return 429 Too Many Requests with appropriate headers
    // Include cached data in the response if available for better UX
    const responseData = cache.data || {
      magicEditsUsed: 0,
      magicEditLimit: MAGIC_EDIT_LIMITS.free,
      magicEditsRemaining: MAGIC_EDIT_LIMITS.free,
      isPaidUser: false,
      bookMemoryEnabled: false,
      plan: 'free'
    };
    
    // Compute when the client can retry (reset time is when oldest request expires)
    const oldestRequest = Math.min(...requestLog[key]);
    const resetTimeSeconds = Math.max(1, Math.ceil((oldestRequest + TIME_WINDOW_MS - now) / 1000));
    
    // Return 429 with appropriate headers and cached data
    return new NextResponse(JSON.stringify(responseData), {
      status: 429,
      headers: {
        'Content-Type': 'application/json',
        'Retry-After': resetTimeSeconds.toString(),
        'X-RateLimit-Limit': effectiveMaxRequests.toString(),
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': (Math.floor(now / 1000) + resetTimeSeconds).toString()
      }
    });
  }

  // Log this request
  requestLog[key].push(now);
  
  // Prepare rate limit headers for successful responses
  const rateLimitHeaders = {
    'X-RateLimit-Limit': effectiveMaxRequests.toString(),
    'X-RateLimit-Remaining': remainingRequests.toString(),
    'X-RateLimit-Reset': (Math.floor(now / 1000) + TIME_WINDOW_MS / 1000).toString()
  };

  // Check if we should bypass the cache (e.g., after a rewrite)
  const bypassCache = request.headers.get('cache-control') === 'no-cache' ||
                      request.nextUrl.searchParams.has('refresh');

  // Log all headers for debugging
  console.log('[user-credits] Request headers:', {
    authorization: request.headers.get('authorization') ? 'present' : 'missing',
    cacheControl: request.headers.get('cache-control'),
    refresh: request.nextUrl.searchParams.has('refresh')
  });

  // If explicitly asked to refresh, clear the cache completely
  if (bypassCache) {
    console.log('[user-credits] Clearing cache due to refresh request');
    cache.data = null;
    cache.timestamp = 0;
  }

  // Check cache first to prevent excessive database queries (unless bypassing)
  if (!bypassCache && cache.data && (now - cache.timestamp) < CACHE_TTL) {
    console.log('[user-credits] Returning cached data');
    return new NextResponse(JSON.stringify(cache.data), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'X-Cache': 'HIT',
        ...rateLimitHeaders
      }
    });
  }

  // ————————————————
  // 1. Authenticate using our unified auth helper
  // ————————————————
  const { user, supabase, authError } = await authenticateApiRequest(request);

  // For user-credits endpoint, allow unauthenticated requests with free tier defaults
  if (!user) {
    console.log('[user-credits] User not authenticated, returning free defaults');
    // not logged in → free defaults
    const defaultResponse = {
      magicEditsUsed: 0,
      magicEditLimit: MAGIC_EDIT_LIMITS.free,
      magicEditsRemaining: MAGIC_EDIT_LIMITS.free,
      isPaidUser: false,
      bookMemoryEnabled: false,
      plan: 'free'
    };
    
    // Cache for unauthenticated users too
    cache.data = defaultResponse;
    cache.timestamp = Date.now();
    
    return new NextResponse(JSON.stringify(defaultResponse), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        ...rateLimitHeaders
      }
    });
  }

  console.log('[user-credits] User authenticated:', user.id);

  // ————————————————
  // 3. Check subscription table
  // ————————————————
  const { data: subscription, error: subError } = await supabase
    .from('subscriptions')
    .select('id')
    .eq('user_id', user.id)
    .is('canceled_at', null)
    .order('created_at', { ascending: false })
    .limit(1)
    .maybeSingle()

  const isPaidUser = !subError && subscription !== null
  const magicEditLimit = isPaidUser ? MAGIC_EDIT_LIMITS.paid : MAGIC_EDIT_LIMITS.free

  // ————————————————
  // 4. Calculate Magic Edits used this month
  // ————————————————
  let magicEditsUsed = 0
  try {
    // Always get a fresh count from the database
    console.log(`[user-credits] Getting Magic Edit count for user ${user.id}`);

    // Direct database query to get the most up-to-date count
    const { count, error } = await supabase
      .from('magic_edits')
      .select('id', { count: 'exact' })
      .eq('user_id', user.id)
      .gte('created_at', new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString());

    if (error) {
      throw error;
    }

    magicEditsUsed = count || 0;
    console.log(`[user-credits] Database count: ${magicEditsUsed} Magic Edits`);
  } catch (error) {
    console.error("Error getting monthly magic edits count:", error);
    // swallow, default to zero
  }

  // ————————————————
  // 5. Cache and return JSON
  // ————————————————
  const responseData = {
    magicEditsUsed,
    magicEditLimit,
    magicEditsRemaining: Math.max(0, magicEditLimit - magicEditsUsed),
    isPaidUser,
    bookMemoryEnabled: isPaidUser,
    plan: isPaidUser ? 'paid' : 'free'
  };

  // Update cache
  cache.data = responseData;
  cache.timestamp = Date.now();
  console.log('[user-credits] Updated cache');

  // Return response with rate limit headers
  return new NextResponse(JSON.stringify(responseData), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      ...rateLimitHeaders
    }
  });
}
