import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  const body = await request.text();
  const signature = request.headers.get('stripe-signature')!;
  
  try {
    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );

    console.log(`Processing Stripe webhook event: ${event.type}`);
    
    // Handle different event types
    switch (event.type) {
      // Handle subscription creation & updates
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;
        
        // Handle one-time purchases (credit packs)
        if (session.mode === 'payment' && session.amount_total) {
          await handleCreditPurchase(session);
        }
        
        // Handle subscription checkout
        if (session.mode === 'subscription' && session.subscription) {
          await handleSubscriptionCheckout(session);
        }
        
        break;
      }
      
      // Handle subscription updates
      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionUpdate(subscription);
        break;
      }
      
      // Handle subscription cancellations
      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionCancellation(subscription);
        break;
      }
      
      // Handle failed payments
      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice;
        if (invoice.subscription) {
          await handleFailedPayment(invoice);
        }
        break;
      }
    }
    
    return NextResponse.json({ received: true });
  } catch (err) {
    console.error('Webhook error:', err);
    return NextResponse.json(
      { error: 'Webhook error' },
      { status: 400 }
    );
  }
}

// Handle one-time credit purchases
async function handleCreditPurchase(session: Stripe.Checkout.Session) {
  const customerEmail = session.customer_email;
  const userId = session.metadata?.userId;
  
  // Determine credit amount based on price
  let creditAmount = 0;
  const amount = session.amount_total; // Amount in cents
  
  if (amount === 200) creditAmount = 1;        // $2.00 = Single Credit
  else if (amount === 800) creditAmount = 5;   // $8.00 = 5 Credits
  else if (amount === 3000) creditAmount = 25; // $30.00 = 25 Credits
  else if (amount === 10000) creditAmount = 100; // $100.00 = 100 Credits
  
  if (creditAmount > 0) {
    // Get user ID either from metadata or by looking up email
    let userIdentifier = userId;
    
    if (!userIdentifier && customerEmail) {
      // Find user by email
      const { data: userData } = await supabase
        .from('users')
        .select('id')
        .eq('email', customerEmail)
        .single();
      
      if (userData) {
        userIdentifier = userData.id;
      }
    }
    
    if (userIdentifier) {
      // Add credits to user
      await supabase.rpc('add_magic_edit_credits', {
        user_id: userIdentifier,
        credits_to_add: creditAmount
      });
      
      console.log(`Added ${creditAmount} credits to user ${userIdentifier}`);
    } else {
      console.error(`User not found for email: ${customerEmail} or userId: ${userId}`);
    }
  }
}

// Handle subscription checkout completion
async function handleSubscriptionCheckout(session: Stripe.Checkout.Session) {
  // Extract customer email and metadata
  const customerEmail = session.customer_email;
  const userId = session.metadata?.userId;
  const subscriptionId = session.subscription as string;
  
  if (!customerEmail && !userId) {
    console.error('No customer email or user ID found in session');
    return;
  }
  
  try {
    // Retrieve the subscription to get more details
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    
    // Get user ID either from metadata or by looking up email
    let userIdentifier = userId;
    
    if (!userIdentifier && customerEmail) {
      // Find user by email
      const { data: userData } = await supabase
        .from('users')
        .select('id')
        .eq('email', customerEmail)
        .single();
      
      if (userData) {
        userIdentifier = userData.id;
      }
    }
    
    if (!userIdentifier) {
      console.error('Could not identify user from session');
      return;
    }
    
    // Insert subscription record
    const { error } = await supabase
      .from('subscriptions')
      .insert({
        user_id: userIdentifier,
        stripe_subscription_id: subscriptionId,
        stripe_customer_id: subscription.customer as string,
        status: subscription.status,
        price_id: subscription.items.data[0]?.price.id,
        quantity: subscription.items.data[0]?.quantity || 1,
        plan_type: subscription.items.data[0]?.price.recurring?.interval || 'month',
        current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
        created_at: new Date().toISOString()
      });
    
    if (error) {
      console.error('Error inserting subscription record:', error);
      return;
    }
    
    console.log(`Added subscription ${subscriptionId} for user ${userIdentifier}`);
  } catch (error) {
    console.error('Error processing subscription checkout:', error);
  }
}

// Handle subscription updates
async function handleSubscriptionUpdate(subscription: Stripe.Subscription) {
  try {
    // Get the customer ID
    const customerId = subscription.customer as string;
    
    // Find user by stripe customer ID
    const { data: subscriptionData } = await supabase
      .from('subscriptions')
      .select('id, user_id')
      .eq('stripe_customer_id', customerId)
      .eq('stripe_subscription_id', subscription.id)
      .single();
    
    if (!subscriptionData) {
      console.error(`No subscription found for customer ${customerId}`);
      return;
    }
    
    // Update subscription record
    const { error } = await supabase
      .from('subscriptions')
      .update({
        status: subscription.status,
        price_id: subscription.items.data[0]?.price.id,
        quantity: subscription.items.data[0]?.quantity || 1,
        plan_type: subscription.items.data[0]?.price.recurring?.interval || 'month',
        current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', subscriptionData.id);
    
    if (error) {
      console.error('Error updating subscription record:', error);
      return;
    }
    
    console.log(`Updated subscription ${subscription.id} for user ${subscriptionData.user_id}`);
  } catch (error) {
    console.error('Error processing subscription update:', error);
  }
}

// Handle subscription cancellations
async function handleSubscriptionCancellation(subscription: Stripe.Subscription) {
  try {
    // Get the customer ID
    const customerId = subscription.customer as string;
    
    // Find subscription in our database
    const { data: subscriptionData } = await supabase
      .from('subscriptions')
      .select('id, user_id')
      .eq('stripe_customer_id', customerId)
      .eq('stripe_subscription_id', subscription.id)
      .single();
    
    if (!subscriptionData) {
      console.error(`No subscription found for customer ${customerId}`);
      return;
    }
    
    // Update subscription record with canceled status and timestamp
    const { error } = await supabase
      .from('subscriptions')
      .update({
        status: 'canceled',
        canceled_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', subscriptionData.id);
    
    if (error) {
      console.error('Error updating subscription cancellation:', error);
      return;
    }
    
    console.log(`Marked subscription ${subscription.id} as canceled for user ${subscriptionData.user_id}`);
  } catch (error) {
    console.error('Error processing subscription cancellation:', error);
  }
}

// Handle failed payments
async function handleFailedPayment(invoice: Stripe.Invoice) {
  try {
    const subscriptionId = invoice.subscription as string;
    
    // Find subscription in our database
    const { data: subscriptionData } = await supabase
      .from('subscriptions')
      .select('id, user_id')
      .eq('stripe_subscription_id', subscriptionId)
      .single();
    
    if (!subscriptionData) {
      console.error(`No subscription found for invoice ${invoice.id}`);
      return;
    }
    
    // Update subscription record with payment issue flag
    const { error } = await supabase
      .from('subscriptions')
      .update({
        has_payment_issue: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', subscriptionData.id);
    
    if (error) {
      console.error('Error updating payment issue status:', error);
      return;
    }
    
    console.log(`Marked payment issue for subscription ${subscriptionId} (user ${subscriptionData.user_id})`);
  } catch (error) {
    console.error('Error processing failed payment:', error);
  }
}