import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { authenticateApiRequest } from '@/lib/supabase/api-auth';

// Initialize Stripe with the secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

// Prices for different plans and credit packages
const PRICES = {
  monthly: process.env.STRIPE_PRICE_ID_MONTHLY || 'price_monthly', // Replace with actual price ID (deprecated)
  annual: process.env.STRIPE_PRICE_ID_ANNUAL || 'price_annual',    // Replace with actual price ID (deprecated)
  credit_single: 'price_1RSVrYBX3CJD0QyTiHhaS0kD',   // Single Credit - $2
  credit_5: 'price_1RSVsFBX3CJD0QyTixvgzbCK',        // Novella Pack (5 Credits) - $8
  credit_25: 'price_1RSVt7BX3CJD0QyTlCyQpcRE',       // Novel Pack (25 Credits) - $30
  credit_100: 'price_1RQjVfBX3CJD0QyTJMsb9GXS',      // Epic Saga Pack (100 Credits) - $100
};

export async function POST(request: NextRequest) {
  try {
    // Authenticate the request
    const { user, authError } = await authenticateApiRequest(request);
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized: Please sign in to upgrade your account' },
        { status: 401 }
      );
    }
    
    // Parse request body
    const body = await request.json();
    const { planType = 'monthly', successUrl, cancelUrl } = body;
    
    // Validate inputs
    const validPlanTypes = ['monthly', 'annual', 'credit_single', 'credit_5', 'credit_25', 'credit_100'];
    if (!validPlanTypes.includes(planType)) {
      return NextResponse.json(
        { error: `Invalid plan type. Choose one of: ${validPlanTypes.join(', ')}` },
        { status: 400 }
      );
    }
    
    // Set price based on plan type
    const priceId = PRICES[planType as keyof typeof PRICES];
    
    // Default URLs if not provided
    const defaultSuccessUrl = `https://bookwriter.vip/dashboard?checkout=success`;
    const defaultCancelUrl = `https://bookwriter.vip/pricing?checkout=canceled`;
    
    // Determine checkout mode based on plan type
    const isSubscription = ['monthly', 'annual'].includes(planType);
    const mode = isSubscription ? 'subscription' : 'payment';
    
    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      billing_address_collection: 'auto',
      customer_email: user.email,
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode,
      metadata: {
        userId: user.id,
        planType,
      },
      success_url: successUrl || defaultSuccessUrl,
      cancel_url: cancelUrl || defaultCancelUrl,
    });
    
    // Return the session URL
    return NextResponse.json({ url: session.url });
    
  } catch (error: any) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}