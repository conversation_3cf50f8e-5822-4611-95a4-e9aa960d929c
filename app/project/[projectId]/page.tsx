'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';

import { supabase } from '@/lib/supabase/client';
import { createChapter, getBookChapters } from '@/lib/supabase/database'; // Assuming these are fine
import { processBookMemory } from '@/lib/supabase/bookmemory';
import { Navbar } from '@/components/navbar';
import { EditorPanel, EditorPanelRef } from './components/editor-panel'; // Assuming EditorPanelRef will be exported
import { ChapterSheet } from './components/chapter-sheet';
import { MainToolbar } from './components/main-toolbar';
import { WordCountBadge } from './components/word-count-badge';
import { useAuth } from '@/lib/providers/auth-provider';
import { MagicEditDialog } from './components/magic-edit-dialog'; // Import the Magic Edit dialog
import { useUserPlan } from '@/hooks/use-user-plan'; // For credit info

import type { Database } from '@/lib/supabase/database.types'; // Import Database type

// Type definitions aligned with database.types.ts
// Using types directly from Database for stricter checking might be even better,
// but for now, let's align the local interfaces.
type ProjectRow = Database['public']['Tables']['projects']['Row'];
type ChapterRow = Database['public']['Tables']['chapters']['Row'];

// Define Project interface with fields matching ProjectRow for shared properties,
// and add client-specific ones.
interface Project {
  id: string;
  title: string;
  user_id: string;
  goal: number; // Matches ProjectRow.goal
  cover_image_url: string | null; // Matches ProjectRow.cover_image_url
  created_at: string;
  updated_at: string;
  current_word_count?: number; // Match ProjectRow (number | undefined). Handle null explicitly if needed.

  // Client-side only fields, optional
  author?: string;
  template_name?: string;
}

export interface Chapter extends ChapterRow {
  // Ensure local Chapter matches ChapterRow
  id: string;
  project_id: string;
  title: string;
  subtitle: string | null;
  content: string;
  chapter_order: number;
  word_count: number;
  created_at: string; // No longer optional
  updated_at: string; // No longer optional
}

// Helper for debounce, simplified
const debounce = <F extends (...args: any[]) => any>(func: F, waitFor: number) => {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  return (...args: Parameters<F>): Promise<ReturnType<F>> =>
    new Promise(resolve => {
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(() => resolve(func(...args)), waitFor);
    });
};

export default function WritingDeskPage() {
  // Zen Mode state lifted to parent
  const [zenMode, setZenMode] = useState(false);
  const params = useParams();
  const router = useRouter();
  const projectId = params.projectId as string;
  const { user, getAuthToken } = useAuth(); // getAuthToken for API calls

  const [project, setProject] = useState<Project | null>(null);
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [activeChapter, setActiveChapter] = useState<Chapter | null>(null);
  const [isLoadingProject, setIsLoadingProject] = useState(true); // More specific name
  const [showChapterSheet, setShowChapterSheet] = useState(false);
  const [showSettingsDialog, setShowSettingsDialog] = useState(false);

  const editorPanelRef = useRef<EditorPanelRef>(null); // Ref for EditorPanel methods

  // Magic Edit™ Feature State
  const [showMagicEditDialog, setShowMagicEditDialog] = useState(false);
  const [textForMagicEditDialog, setTextForMagicEditDialog] = useState(''); // HTML content for dialog
  const [isGeneratingMagicEdit, setIsGeneratingMagicEdit] = useState(false);
  const [generatedMagicEditSuggestion, setGeneratedMagicEditSuggestion] = useState<string | null>(null);
  // New: Magic Edit chapter selection and chunking
  const [selectedMagicEditChapterId, setSelectedMagicEditChapterId] = useState<string | null>(null);
  const [magicEditChunkIndex, setMagicEditChunkIndex] = useState(0);

  // Magic Edit loading overlay state
  const [magicEditProgress, setMagicEditProgress] = useState(0); // 0-100
  const [magicEditThought, setMagicEditThought] = useState('');
  const [showMagicEditLoading, setShowMagicEditLoading] = useState(false);

  // Creative "thoughts" for progress simulation
  const magicEditThoughts = [
    "Reading Document...",
    "Analyzing Structure...",
    "Studying Your Writing Technique...",
    "Appreciating Your Story...",
    "Detecting Errors...",
    "Error Detected? None Found.",
    "Processing Grammar...",
    "Refining Flow...",
    "Enhancing Clarity...",
    "Polishing Sentences...",
    "Injecting Professional Style...",
    "Finalizing Edits...",
    "Merging Results...",
    "Reviewing Output...",
    "Magic Complete!"
  ];
  // Map progress points to thoughts (0%, 10%, ..., 100%)
  const magicEditThoughtMap = [0, 7, 15, 22, 30, 37, 45, 52, 60, 67, 75, 82, 90, 97, 100];

  const userPlanDetails = useUserPlan(user?.id);

  useEffect(() => {
    const fetchProjectData = async () => {
      if (!user || !projectId) {
        setIsLoadingProject(false);
        if (!user) router.push('/login');
        return;
      }
      setIsLoadingProject(true);
      try {
        const projectQuery = await supabase
          .from('projects')
          .select('*')
          .eq('id', projectId) // projectId is string, matches DB type
          .eq('user_id', user.id) // user.id is string, matches DB type
          .single();

        if (projectQuery.error) {
          console.error('Error fetching project:', projectQuery.error);
          throw projectQuery.error;
        }
        if (!projectQuery.data) { // Check if data is null
          throw new Error("Project not found or access denied.");
        }
        const dbProjectData = projectQuery.data as ProjectRow; // Assert type after checks
        setProject({
            id: dbProjectData.id,
            title: dbProjectData.title,
            user_id: dbProjectData.user_id,
            goal: dbProjectData.goal,
            cover_image_url: dbProjectData.cover_image_url,
            created_at: dbProjectData.created_at,
            updated_at: dbProjectData.updated_at,
            current_word_count: dbProjectData.current_word_count === null ? undefined : dbProjectData.current_word_count,
            // author and template_name are client-side, will be undefined initially
        });

        const chaptersQuery = await supabase
          .from('chapters')
          .select('*')
          .eq('project_id', projectId) // projectId is string, matches DB type
          .order('chapter_order', { ascending: true });

        if (chaptersQuery.error) {
          console.error('Error fetching chapters:', chaptersQuery.error);
          throw chaptersQuery.error;
        }

        const fetchedDBChapters = (chaptersQuery.data || []) as ChapterRow[];
        // Cast to local Chapter type, assuming compatibility is maintained
        const fetchedChapters: Chapter[] = fetchedDBChapters.map(ch => ({...ch}) as Chapter);
        setChapters(fetchedChapters);

        if (fetchedChapters.length > 0) {
          setActiveChapter(fetchedChapters[0]);
        } else {
          setActiveChapter(null);
        }
      } catch (error: any) {
        console.error('Error fetching project data:', error.message);
        toast.error(`Failed to load project: ${error.message}`);
        if (error.code === 'PGRST001' || error.code === 'PGRST116') {
             router.push('/dashboard');
        }
      } finally {
        setIsLoadingProject(false);
      }
    };
    fetchProjectData();
  }, [projectId, user, router]);

  const debouncedUpdateChapterContent = useCallback(
    debounce(async (chapterId: string, newContent: string, newWordCount: number) => {
      if (!user || !activeChapter) return; // Ensure activeChapter is also available
      try {
        const { error } = await supabase
          .from('chapters')
          .update({ content: newContent, word_count: newWordCount, updated_at: new Date().toISOString() })
          .eq('id', chapterId)
          .eq('project_id', projectId);

        if (error) throw error;

        setChapters(prev => prev.map(ch => ch.id === chapterId ? {...ch, content: newContent, word_count: newWordCount, updated_at: new Date().toISOString()} : ch));
        // No need to update activeChapter separately if chapters update triggers its own useEffect sync

        await calculateProjectWordCount(); // Recalculate project word count
        toast.success("Changes saved automatically.");

      } catch (error: any) {
        console.error("Error debounced saving chapter:", error);
        toast.error("Failed to save changes automatically.");
      }
    }, 1500),
    [projectId, user?.id, activeChapter?.id]
  );

  const handleContentUpdate = (newHtmlContent: string, wordCount: number) => {
    if (activeChapter) {
      // Optimistically update activeChapter's content for UI responsiveness
      // The actual chapter in the 'chapters' array will be updated by debouncedSave
      setActiveChapter(prev => prev ? { ...prev, content: newHtmlContent, word_count: wordCount } : null);
      debouncedUpdateChapterContent(activeChapter.id, newHtmlContent, wordCount);
    }
  };

  const handleSubtitleUpdate = async (newSubtitle: string | null) => {
    if (!activeChapter || !user) return;
    try {
      const { error } = await supabase
        .from('chapters')
        .update({ subtitle: newSubtitle, updated_at: new Date().toISOString() })
        .eq('id', activeChapter.id);
      if (error) throw error;
      const updatedChapters = chapters.map(ch => ch.id === activeChapter.id ? {...ch, subtitle: newSubtitle, updated_at: new Date().toISOString()} : ch);
      setChapters(updatedChapters);
      setActiveChapter(prev => prev ? {...prev, subtitle: newSubtitle, updated_at: new Date().toISOString()} : null);
      toast.success("Subtitle updated.");
    } catch (error: any) {
      toast.error(`Failed to update subtitle: ${error.message}`);
    }
  };

  const calculateProjectWordCount = async () => {
    // Use a local copy of chapters for calculation to ensure consistency within this function
    const currentChaptersForCalc = chapters; // Use a stable variable for the reduce
    if (!project || currentChaptersForCalc.length === 0) {
      if (project && project.current_word_count !== 0) {
         // Ensure update object matches projects.Update type
         const payload: Database['public']['Tables']['projects']['Update'] = { current_word_count: 0, updated_at: new Date().toISOString() };
         await supabase.from('projects').update(payload).eq('id', projectId);
         setProject(prev => prev ? { ...prev, current_word_count: 0 } : null);
      }
      return;
    }
    const totalWords = currentChaptersForCalc.reduce((sum, ch) => sum + (ch.word_count || 0), 0);
    if (project.current_word_count !== totalWords) {
      try {
        // Ensure update object matches projects.Update type
        const payload: Database['public']['Tables']['projects']['Update'] = { current_word_count: totalWords, updated_at: new Date().toISOString() };
        const { error } = await supabase.from('projects').update(payload).eq('id', projectId);
        if (error) throw error;
        setProject(prev => prev ? { ...prev, current_word_count: totalWords } : null);
      } catch (error: any) {
        console.error("Failed to update project word count in DB", error.message);
        // Do not toast here as it can be frequent
      }
    }
  };

  useEffect(() => {
    if (project && chapters) calculateProjectWordCount();
  }, [chapters, project]); // Depend on chapters and project object references

  const handleTriggerMagicEdit = async () => {
    if (editorPanelRef.current) {
      // Auto-select and highlight text for Magic Edit
      const selectedData = editorPanelRef.current.autoSelectForMagicEdit();

      if (selectedData && selectedData.html && selectedData.wordCount > 0) {
        // Store selection and prepare for dialog
        setTextForMagicEditDialog(selectedData.html);
        editorPanelRef.current.storeOriginalSelection();
        setGeneratedMagicEditSuggestion(null);

        const creditsRequired = Math.ceil(selectedData.wordCount / 5000);

        // Refresh credits to ensure accurate display
        await userPlanDetails.refetch();

        toast.success(`Selected ${selectedData.wordCount} words for Magic Edit™ (${creditsRequired} credit${creditsRequired !== 1 ? 's' : ''}). Text is highlighted in editor.`);

        // Open dialog immediately to show the selection
        setShowMagicEditDialog(true);
      } else {
        toast.info("No unedited content available for Magic Edit™, or the content is too short.");
      }
    }
  };

  const handleGenerateMagicEditApiCall = async (textToEditHtml: string) => {
    if (!projectId || !activeChapter?.id || !user) {
      toast.error("Missing project, chapter, or user information for Magic Edit.");
      return;
    }
    setIsGeneratingMagicEdit(true);
    setGeneratedMagicEditSuggestion(null);
    const authToken = await getAuthToken();
    if (!authToken) {
        toast.error("Authentication token not found. Please try logging in again.");
        setIsGeneratingMagicEdit(false);
        return;
    }

    try {
      const response = await fetch('/api/magic-edit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
        },
        body: JSON.stringify({
          projectId,
          chapterId: activeChapter.id,
          htmlContent: textToEditHtml,
          userId: user.id,
        }),
      });

      const responseBody = await response.text(); // Get raw response body

      if (!response.ok) {
        let errorData = { error: `Magic Edit failed with status ${response.status}` };
        try {
          errorData = JSON.parse(responseBody); // Try to parse as JSON
        } catch (e) {
          // If not JSON, use the raw text or a generic message
          console.error("Failed to parse Magic Edit error response as JSON:", responseBody);
        }
        throw new Error(errorData.error || `Magic Edit failed: ${responseBody}`);
      }

      const data = JSON.parse(responseBody); // Parse successful response
      setGeneratedMagicEditSuggestion(data.html);

      // Force refresh credits and dispatch event
      await userPlanDetails.refetch();

      // Dispatch event to update credits display after refetch
      if (window) {
        const event = new CustomEvent('magic-edit-completed', {
          detail: { timestamp: Date.now() }
        });
        window.dispatchEvent(event);
      }

      toast.success("Magic Edit™ completed!");
    } catch (error: any) {
      console.error("Magic Edit API error:", error);
      toast.error(`Magic Edit failed: ${error.message}`);
      setGeneratedMagicEditSuggestion(null);
    } finally {
      setIsGeneratingMagicEdit(false);
    }
  };

  const handleAcceptMagicEditToScrapPaper = (editedHtml: string) => {
    if (editorPanelRef.current) {
      editorPanelRef.current.updateScrapPaper(editedHtml);
      editorPanelRef.current.switchToScrapPaperTab();
    }
    setShowMagicEditDialog(false);
  };

  const handleCreateNewChapter = async () => {
    if (!projectId || !user) {
      toast.error("Cannot create chapter: Missing project or user.");
      return;
    }
    const loadingToastId = toast.loading("Creating new chapter...");
    try {
      // Optional: Explicitly save current chapter before creating new one
      if (editorPanelRef.current) {
        editorPanelRef.current.triggerSave();
        await new Promise(resolve => setTimeout(resolve, 500)); // Small delay for save to process
      }

      const newOrder = chapters.length > 0 ? Math.max(...chapters.map(ch => ch.chapter_order)) + 1 : 1;
      const newChapterData = await createChapter(projectId, `Chapter ${newOrder}`, newOrder, "<p></p>", null);
      if (!newChapterData) throw new Error("Failed to create chapter record.");

      const updatedChaptersList = await getBookChapters(projectId);
      setChapters(updatedChaptersList as Chapter[]);
      setActiveChapter(newChapterData as Chapter);

      toast.success("New chapter created!", { id: loadingToastId });
    } catch (error: any) {
      toast.error(`Failed to create chapter: ${error.message}`, { id: loadingToastId });
    }
  };

  useEffect(() => {
    if (chapters.length > 0 && activeChapter) {
      const currentActiveStillExists = chapters.find(ch => ch.id === activeChapter.id);
      if (currentActiveStillExists) {
        if (JSON.stringify(currentActiveStillExists) !== JSON.stringify(activeChapter)) {
           setActiveChapter(currentActiveStillExists);
        }
      } else if (chapters.length > 0) {
        setActiveChapter(chapters[0]);
      } else {
         setActiveChapter(null);
      }
    } else if (chapters.length > 0 && !activeChapter) {
        setActiveChapter(chapters[0]); // If no active chapter but chapters exist (e.g. after initial load delay)
    } else if (chapters.length === 0) {
      setActiveChapter(null);
    }
  }, [chapters]); // Removed activeChapter.id from deps to avoid loop with its own update

  if (isLoadingProject) {
    return (
      <div className="flex min-h-screen flex-col"><Navbar />
        <main className="flex-1 flex items-center justify-center"><Loader2 className="h-8 w-8 animate-spin mr-2" /> Loading Project...</main>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="flex min-h-screen flex-col"><Navbar />
        <main className="flex-1 flex items-center justify-center text-center p-4">
          <div><h2 className="text-xl font-bold mb-2">Project Not Found</h2><p className="text-muted-foreground">This project may not exist or you don't have access.</p><Button onClick={() => router.push('/dashboard')} className="mt-6">Go to Dashboard</Button></div>
        </main>
      </div>
    );
  }

  if (chapters.length === 0 && !activeChapter && !isLoadingProject) { // Added !isLoadingProject to prevent flash
     return (
      <div className="flex min-h-screen flex-col"><Navbar />
        <main className="flex-1 flex items-center justify-center text-center p-4">
          <div>
            <h2 className="text-xl font-bold mb-2">Welcome to "{project.title}"!</h2>
            <p className="text-muted-foreground mb-6">Let's start by creating your first chapter.</p>
            <Button onClick={handleCreateNewChapter}>Create Chapter 1</Button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col bg-[#000000]"> {/* Pure black background */}
      {/* Custom override styles for editor */}
      <style jsx global>{`
        /* Ensure our editor design takes precedence */
        .editor-panel {
          background-color: #000000 !important;
        }

        /* Force overlay for word count and save status to show correctly */
        .fixed {
          z-index: 1000 !important;
        }

        /* Force modern styling on tabs */
        .bg-black\\/60 {
          background-color: rgba(0, 0, 0, 0.6) !important;
          backdrop-filter: blur(8px) !important;
        }

        /* Ensure gradient buttons display correctly */
        .bg-gradient-to-r.from-amber-600.to-amber-500 {
          background: linear-gradient(90deg, #FFD700 0%, #FFC107 100%) !important;
          color: black !important;
          box-shadow: 0 0 15px rgba(255, 215, 0, 0.2) !important;
        }

        /* Style improvements for editor area */
        .editor-container {
          position: relative !important;
        }

        /* Style the scrap paper area better */
        .bg-gradient-to-b.from-amber-950\\/30.to-amber-900\\/10 {
          background: linear-gradient(180deg, rgba(30, 20, 0, 0.4) 0%, rgba(20, 10, 0, 0.2) 100%) !important;
          border: 1px solid rgba(255, 215, 0, 0.2) !important;
        }

        /* Magic Edit preview selection highlighting */
        .magic-edit-preview-selection,
        span[data-magic-edit-preview="true"] {
          background: linear-gradient(135deg, rgba(255, 215, 0, 0.15) 0%, rgba(255, 193, 7, 0.25) 100%) !important;
          border: 2px solid rgba(255, 215, 0, 0.4) !important;
          border-radius: 4px !important;
          padding: 2px 4px !important;
          margin: 0 2px !important;
          box-shadow: 0 0 8px rgba(255, 215, 0, 0.3) !important;
          animation: magic-edit-pulse 2s ease-in-out infinite !important;
        }

        @keyframes magic-edit-pulse {
          0%, 100% {
            box-shadow: 0 0 8px rgba(255, 215, 0, 0.3);
          }
          50% {
            box-shadow: 0 0 16px rgba(255, 215, 0, 0.5);
          }
        }

        /* Apply paper texture to editor */
        .editor-container::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAAAXNSR0IArs4c6QAABCRJREFUaEPtmltojmEYx3+HmR1ITrNmzOEqc5oUuZAlSly4ECU5lFKUlEsuXIjblAsiFy7kTG6UC0qUUnKWHOZQyKHMIWZmGrt6v6/eft/3fO/7vFt25f2+ms7P//k/z/N/DkrpRDQc2AAMA/qJ3HsBvA3lCQ0QKxM6IBZGdECsQHRArEDUQCxB1EAsQVRAbEHUQGxBVEDqAcQMRBXEDEQVxAREHcQERAXECkQcxApEHMQSRBTEEkQUxBpEDMQaRAykXkDEQOoFRATEGiLQVlaKRqrbYx7ihEks5LT1kaRKeDMoR+OKOYgTJmqCagGiLShHt7sZiBOmxoSL+VFU+C5eUMcMxAlzzJibRBXZhlSVwNbdvgzECTPfkJdErHS2YALZVSkv9IYgTpi5AQCCpgpI6swZYK8vQwOQJMwhY1KlqnhDKlfA5xtlOL6NHRCxYn4KhZPLLZdUFoRK93KoKlIyhUy+JlV5qoHk8qnSvRyqAjKhVcGvK6Pn7AZZFrrPbynVDCQZkERTifyfctUUzpoqLMnWEfOiSppcWQPMUPbtKfB7E7wKOV9wCuS+hGvPpd8QM5D3x6D4lKSS+H1rQB9PQ9dhAV8vgdJzErSFFlmDot/APokVDcKtc9A9LSOnJtmFgMwMq111FJbfkiBNtElDzhlhqjTzkLUsKFgCvebEw4QpIDKoJlLkPoGnR/wwtYCpfiwrKiugKiRymvtVa+vluFj9PqgRSHL+alq6NjXOXw3V/3zkLErA/DP+XgrvDkLVV+jQB4qXQr8p8PsWRL6EK8B2G8ik8TDqAHQointp1Sl4tgEqnhntlRRBxU/AXgP6z4JW7SH/vD2MClbByEP+tc+2w+M1bmCsMvLiMJSsj8tOPQlthxl7C4YwZ8AjQ5zK/8omQb9pMPBsHGbocnuYwFijKpwvhoqvNZINmAeN88CTxfG5PjNg+P44yG5gJqvMNoGLo/yfkLJH0LQAFNX4X+zRfviwA9oMgcKl0G0MRCrg00GoKPEDGTYPhi6BCw3iYQ1cDI8j8PRgTNzwHVDsKQcqWKYw+/8n0noIDN0OzXvFl5UegRcbobnnahHb8hq4Osn/8euPQ+HimvN3ACaO9YdRuNZfNlv3RmljdEdlGnYcCRR/8Md6+wTKAu75tiv9dt4pt3X/9W887wGV7+HGuJo/ladw7DEQEE/dSnW03ckR9gD1Vg5s95lnkNYZT9L2AbLxkPRicKUpZw1xm41KFiS7zJtcVcfzUE05MvaYH9G0sSdny4G8HBl4zjsfglOi8cr8wNSE4ksf1tap81ioPoJZFPYz26JmyZXMENNyZLPmMncnqzJlVV70c3K14TFandmVF7drbIs7iavbUr7ERt3MRVXVu5jKjarGZ44pyhlDpMqL7lGjp5I0fNwrtpkEkao0LesR801xuniLBsAqgLVovP9sF2rZQcbDYgAAAABJRU5ErkJggg==");
          background-size: 10px;
          opacity: 0.05;
          pointer-events: none;
          z-index: -1;
        }
      `}</style>

      <Navbar />
      <main className="flex-1 mt-16 relative pb-24 md:pb-20"> {/* Adjusted padding for toolbar */}
          <ChapterSheet
            open={showChapterSheet}
            onOpenChange={setShowChapterSheet}
            chapters={chapters}
            activeChapterId={activeChapter?.id || ''}
            setActiveChapter={(chapter) => setActiveChapter(chapter as Chapter)}
            projectId={projectId}
            setChapters={setChapters}
            project={project}
            onOpenSettings={() => setShowSettingsDialog(true)}
            onTriggerMagicEdit={handleTriggerMagicEdit}
          />

        <div className="relative mx-auto">
          {activeChapter && project ? (
            <EditorPanel
              ref={editorPanelRef}
              key={activeChapter.id}
              chapter={activeChapter}
              onContentChange={handleContentUpdate}
              onSubtitleChange={handleSubtitleUpdate}
              zenMode={zenMode}
              setZenMode={setZenMode}
            />
          ) : (
            <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)] w-full p-4"> {/* Adjusted height */}
              <Loader2 className="h-8 w-8 animate-spin mb-4 text-primary" />
              <p className="text-lg text-muted-foreground">Loading chapter content...</p>
            </div>
          )}

          {/* Word count badge removed from here as it's now in the editor */}
        </div>

        <MainToolbar
          onChaptersClick={() => setShowChapterSheet(!showChapterSheet)}
          projectId={projectId}
          projectTitle={project.title}
          onNewChapter={handleCreateNewChapter}
          project={project}
          onProjectUpdate={(updatedFields) => setProject(prev => prev ? {...prev, ...updatedFields} : null)}
          onTriggerMagicEdit={handleTriggerMagicEdit}
          isRewritePossible={!!activeChapter && !!editorPanelRef.current}
          isGeneratingAiContent={isGeneratingMagicEdit}
          zenMode={zenMode}
          setZenMode={setZenMode}
        />

        {activeChapter && project && (
          <MagicEditDialog
            isOpen={showMagicEditDialog}
            onOpenChange={(open) => {
              setShowMagicEditDialog(open);
              // Clear preview selection when dialog closes
              if (!open && editorPanelRef.current) {
                editorPanelRef.current.clearMagicEditSelection();
              }
            }}
            initialTextHtml={textForMagicEditDialog || activeChapter.content || ''}
            isGenerating={isGeneratingMagicEdit}
            generatedSuggestion={generatedMagicEditSuggestion}
            onGenerateMagicEdit={handleGenerateMagicEditApiCall}
            onAcceptToScrapPaper={handleAcceptMagicEditToScrapPaper}
            chapters={chapters.map(ch => ({
              id: ch.id,
              title: ch.title,
              content: ch.content
            }))}
            selectedChapterId={selectedMagicEditChapterId || activeChapter.id}
            onSelectChapter={id => {
              setSelectedMagicEditChapterId(id);
              setMagicEditChunkIndex(0);
            }}
            onAcceptAndEdit={async (chapterId) => {
              // Chunked Magic Edit process with progress/thoughts
              const chapter = chapters.find(ch => ch.id === chapterId);
              if (!chapter) {
                toast.error("Chapter not found.");
                return;
              }
              const tempDiv = document.createElement('div');
              tempDiv.innerHTML = chapter.content || '';
              const text = tempDiv.textContent || tempDiv.innerText || '';
              const words = text.trim().split(/\s+/).filter(Boolean);
              const chunkSize = 5000;
              const numChunks = Math.ceil(words.length / chunkSize);
              let mergedHtml = '';
              // Initial loading state before loop
              setMagicEditProgress(0);
              setMagicEditThought("Preparing Magic Edit...");
              setShowMagicEditLoading(true);
              setIsGeneratingMagicEdit(true);

              try {
                for (let i = 0; i < numChunks; i++) {
                  const chunkWords = words.slice(i * chunkSize, (i + 1) * chunkSize);
                  const chunkHtml = `<p>${chunkWords.join(' ')}</p>`;

                  // Thought for current chunk processing
                  const processingThought = numChunks > 1 ? `Editing segment ${i + 1} of ${numChunks}...` : "Editing your text...";
                  setMagicEditThought(processingThought);
                  // No progress update here yet, only after API call for the chunk
                  await new Promise(resolve => setTimeout(resolve, 50)); // Brief pause for UI to show "Editing segment..."

                  console.log(`Magic Edit Chunk ${i + 1}/${numChunks}:`, chunkHtml); // Log chunk

                  // Call the API for this chunk
                  const authToken = await getAuthToken();
                  const response = await fetch('/api/magic-edit', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                      'Authorization': `Bearer ${authToken}`,
                    },
                    body: JSON.stringify({
                      projectId,
                      chapterId: chapter.id,
                      htmlContent: chunkHtml,
                      userId: user?.id,
                    }),
                  });
                  const responseBody = await response.text();
                  if (!response.ok) {
                    let errorData = { error: `Magic Edit failed with status ${response.status}` };
                    try {
                      errorData = JSON.parse(responseBody);
                    } catch (e) {}
                    throw new Error(errorData.error || `Magic Edit failed: ${responseBody}`);
                  }
                  const data = JSON.parse(responseBody);
                  mergedHtml += data.html || '';

                  // Update progress and thought *after* successful API call for the chunk
                  const percent = Math.round(((i + 1) / numChunks) * 100);
                  setMagicEditProgress(percent);

                  if (percent < 100) {
                    // If more chunks, show a thought about merging or next step
                     setMagicEditThought(numChunks > 1 ? `Segment ${i+1} complete. Merging results...` : "Finalizing edits...");
                  }
                  // The "Magic Complete!" thought will be set in the 'finally' block if all successful
                  await new Promise(resolve => setTimeout(resolve, 50)); // Brief pause for UI
                }

                // All chunks processed successfully
                console.log('Magic Edit Merged HTML:', mergedHtml);
                if (editorPanelRef.current) {
                  editorPanelRef.current.updateScrapPaper(mergedHtml);
                  editorPanelRef.current.switchToScrapPaperTab();
                }
                setShowMagicEditDialog(false);

                // Force refresh credits and dispatch event
                await userPlanDetails.refetch();

                // Dispatch event to update credits display after refetch
                if (window) {
                  const event = new CustomEvent('magic-edit-completed', {
                    detail: { timestamp: Date.now() }
                  });
                  window.dispatchEvent(event);
                }

                toast.success("Magic Edit™ completed and sent to Scrap Paper!");
                // Set final thought here for success
                setMagicEditThought(magicEditThoughts[magicEditThoughts.length - 1]); // "Magic Complete!"
                // Keep loading screen for a moment to show "Magic Complete!"
                await new Promise(resolve => setTimeout(resolve, 1500));

              } catch (error: any) {
                toast.error(`Magic Edit failed: ${error.message}`);
                setGeneratedMagicEditSuggestion(null);
                // Potentially set an error thought
                setMagicEditThought("An error occurred.");
                await new Promise(resolve => setTimeout(resolve, 1500)); // Show error thought briefly
              } finally {
                setIsGeneratingMagicEdit(false);
                setShowMagicEditLoading(false);
                // Reset progress for next time, but thought is handled by success/error paths
                setMagicEditProgress(0);
              }
            }}
          />
        )}
      </main>
      {/* Magic Edit Loading Overlay */}
      {showMagicEditLoading && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100vw',
            height: '100vh',
            background: 'rgba(10,10,20,0.92)',
            zIndex: 3000,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <div
            style={{
              background: 'rgba(30,30,40,0.98)',
              borderRadius: 16,
              padding: '2.5rem 2.5rem 2rem 2.5rem',
              boxShadow: '0 8px 32px #000a',
              minWidth: 340,
              maxWidth: 420,
              textAlign: 'center'
            }}
          >
            <div
              style={{
                fontFamily: 'monospace, monospace',
                fontSize: '1.1rem',
                color: '#FFD700',
                marginBottom: 18,
                minHeight: 32,
                letterSpacing: '0.03em',
                fontWeight: 600,
                textShadow: '0 1px 8px #C2880033'
              }}
            >
              {magicEditThought}
            </div>
            <div
              style={{
                width: '100%',
                height: 18,
                background: '#222',
                borderRadius: 8,
                overflow: 'hidden',
                marginBottom: 8,
                boxShadow: '0 1px 4px #0006'
              }}
            >
              <div
                style={{
                  width: `${magicEditProgress}%`,
                  height: '100%',
                  background: 'linear-gradient(90deg, #FFD700 0%, #C28800 100%)',
                  transition: 'width 0.3s cubic-bezier(.4,2,.6,1)'
                }}
              />
            </div>
            <div style={{ color: '#aaa', fontSize: 13, marginTop: 2 }}>
              {magicEditProgress}% complete
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
