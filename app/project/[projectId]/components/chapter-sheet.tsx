'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useCredits } from '@/lib/hooks/use-credits';
import { 
  PlusCircle, 
  Pencil, 
  Trash2, 
  GripVertical, 
  Loader2,
  Settings,
  BookOpen,
  Sparkles,
  Volume2
} from 'lucide-react';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';

import { supabase } from '@/lib/supabase/client';
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { MagicEditCreditsDisplay } from '@/components/magic-edit-credits-display';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/lib/providers/auth-provider';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialog<PERSON><PERSON>l,
  <PERSON>ert<PERSON><PERSON>og<PERSON><PERSON>nt,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Chapter } from '../page';

interface ChapterSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  chapters: Chapter[];
  activeChapterId: string;
  setActiveChapter: (chapter: Chapter) => void;
  projectId: string;
  setChapters: (chapters: Chapter[]) => void;
  creditsRemaining?: number;
  project?: {
    title: string;
    author?: string;
    word_goal?: number;
    current_word_count?: number;
  };
  onOpenSettings?: () => void;
  onTriggerMagicEdit?: () => void;
}

export function ChapterSheet({
  open,
  onOpenChange,
  chapters,
  activeChapterId,
  setActiveChapter,
  projectId,
  setChapters,
  project,
  onOpenSettings,
  onTriggerMagicEdit,
}: ChapterSheetProps) {
  const { credits } = useCredits();
  const { user } = useAuth();
  const [chapterToRename, setChapterToRename] = useState<Chapter | null>(null);
  const [newChapterTitle, setNewChapterTitle] = useState('');
  const [chapterToDelete, setChapterToDelete] = useState<Chapter | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showSoundPopover, setShowSoundPopover] = useState(false);
  
  // No need for debugging statements now that we're using @hello-pangea/dnd

  const handleSelectChapter = (chapter: Chapter) => {
    setActiveChapter(chapter);
    onOpenChange(false);
  };

  const handleAddChapter = async () => {
    setIsLoading(true);
    // Show loading toast for better user feedback
    const loadingToast = toast.loading("Creating new chapter...");
    
    try {
      // 1. Calculate new chapter order using a more robust method
      const newOrder = chapters.length > 0 
        ? Math.max(...chapters.map(ch => ch.chapter_order || 0)) + 1 
        : 1;
      
      // 2. Create new chapter title with order number
      const newChapterTitle = `Chapter ${newOrder}`;
      
      // Explicitly define content and subtitle
      const newContent = '';
      const newSubtitle = null;
      
      // Log attempt details for debugging
      console.log(`Attempting to create chapter: projectId=${projectId}, title=${newChapterTitle}, order=${newOrder}, content='${newContent}', subtitle=${newSubtitle}`);
      console.log('Current chapters:', chapters);
      
      // 3. Create new chapter in database with word count initialized to 0
      const { data, error } = await supabase
        .from('chapters')
        .insert({
          project_id: projectId, // Match database column name
          title: newChapterTitle,
          content: newContent,
          chapter_order: newOrder,
          word_count: 0,
          subtitle: newSubtitle
        })
        .select()
        .single();
      
      if (error) throw error;
      
      // 4. Process and validate the new chapter
      const newChapter = data as Chapter;
      
      // 5. Update local chapters array with the new chapter
      setChapters([...chapters, newChapter]);
      
      // 6. Set the new chapter as active - ensure it has empty content
      console.log(">>> ChapterSheet: Setting active chapter to newly created:", {
        id: newChapter.id,
        title: newChapter.title,
        content_length: newChapter.content?.length || 0
      });
      
      // Create a fresh copy of the chapter to avoid reference issues
      const freshChapter = {
        ...newChapter,
        content: newChapter.content || '' // Ensure content is not undefined
      };
      
      setActiveChapter(freshChapter);
      
      // 7. Close the chapter sheet after successful creation
      onOpenChange(false);
      
      // 8. Show success notification and dismiss loading toast
      toast.dismiss(loadingToast);
      toast.success('New chapter added');
    } catch (error: any) { // Ensure 'any' type to access properties
      console.error('>>> DETAILED Error adding chapter:', error); // Log whole object
      if (error && error.message) {
        console.error('>>> Error Message:', error.message);
      }
      if (error && error.stack) {
        console.error('>>> Error Stack:', error.stack);
      }
      toast.dismiss(loadingToast); // Ensure loading stops
      toast.error('Failed to add new chapter');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRenameChapter = async () => {
    if (!chapterToRename || !newChapterTitle.trim()) return;
    
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('chapters')
        .update({ title: newChapterTitle })
        .eq('id', chapterToRename.id);
      
      if (error) throw error;
      
      // Update chapters state
      const updatedChapters = chapters.map(ch => 
        ch.id === chapterToRename.id 
          ? { ...ch, title: newChapterTitle } 
          : ch
      );
      setChapters(updatedChapters);
      
      // Update active chapter if it's the one being renamed
      if (chapterToRename.id === activeChapterId) {
        setActiveChapter({ ...chapterToRename, title: newChapterTitle });
      }
      
      toast.success('Chapter renamed');
    } catch (error) {
      console.error('Error renaming chapter:', error);
      toast.error('Failed to rename chapter');
    } finally {
      setIsLoading(false);
      setChapterToRename(null);
      setNewChapterTitle('');
    }
  };

  const handleDeleteChapter = async () => {
    if (!chapterToDelete) return;
    
    if (chapters.length <= 1) {
      toast.error('Cannot delete the last chapter');
      setChapterToDelete(null);
      return;
    }
    
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('chapters')
        .delete()
        .eq('id', chapterToDelete.id);
      
      if (error) throw error;
      
      // Remove chapter from state
      const updatedChapters = chapters.filter(ch => ch.id !== chapterToDelete.id);
      setChapters(updatedChapters);
      
      // If the active chapter was deleted, set a new active chapter
      if (chapterToDelete.id === activeChapterId && updatedChapters.length > 0) {
        setActiveChapter(updatedChapters[0]);
      }
      
      toast.success('Chapter deleted');
    } catch (error) {
      console.error('Error deleting chapter:', error);
      toast.error('Failed to delete chapter');
    } finally {
      setIsLoading(false);
      setChapterToDelete(null);
    }
  };

  const handleDragEnd = async (result: any) => {
    if (!result.destination) return;
    
    const { source, destination } = result;
    if (source.index === destination.index) return;
    
    // Reorder chapters in state
    const reorderedChapters = Array.from(chapters);
    const [moved] = reorderedChapters.splice(source.index, 1);
    reorderedChapters.splice(destination.index, 0, moved);
    
    // Update chapter_order property for each chapter
    const updatedChapters = reorderedChapters.map((ch, index) => ({
      ...ch,
      chapter_order: index + 1, // CORRECTED: Using chapter_order instead of order
    }));
    
    setChapters(updatedChapters);
    
    // Update chapter_order in database
    try {
      const updates = updatedChapters.map(ch => ({
        id: ch.id,
        chapter_order: ch.chapter_order, // CORRECTED: Using chapter_order instead of order
      }));
      
      const { error } = await supabase
        .from('chapters')
        .upsert(updates);
      
      if (error) throw error;
    } catch (error) {
      console.error('Error updating chapter order:', error);
      toast.error('Failed to update chapter order');
    }
  };

  return (
    <>
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetContent side="left">
          <div className="flex flex-col h-full">
            {/* Project Info Header */}
            <div className="mb-6">
                <div className="flex items-center">
                  <BookOpen className="h-5 w-5 mr-2 text-amber-500" />
                  <SheetTitle className="text-lg">{project?.title || "Your Book"}</SheetTitle>
                </div>
              
              {project && (
                <div className="text-sm text-muted-foreground mt-1.5">
                  <div className="flex justify-between items-center mt-2">
                    <div>
                      {project.author && <p>{project.author}</p>}
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs bg-primary/10 px-2 py-0.5 rounded-full">
                          {project.current_word_count?.toLocaleString() || 0} / {project.word_goal?.toLocaleString() || 50000} words
                        </span>
                        <span className="text-xs bg-primary/10 px-2 py-0.5 rounded-full">
                          {Math.round(((project.current_word_count || 0) / (project.word_goal || 50000)) * 100)}% complete
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              {user && (
                <div className="mt-4">
                  <MagicEditCreditsDisplay variant="compact" />
                  {/* Button group under Magic Edit Credits */}
                  <div className="flex flex-wrap gap-2 mt-4">
                    <Button size="sm" variant="outline" className="flex-1 min-w-[90px]">
                      Save
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1 min-w-[90px]">
                      Undo
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1 min-w-[90px]">
                      Export
                    </Button>
                    <Button
                      size="sm"
                      variant="default"
                      className="flex-1 min-w-[90px] bg-gradient-to-r from-amber-500 via-orange-500 to-red-500 text-black font-semibold"
                      onClick={onTriggerMagicEdit}
                    >
                      <Sparkles className="h-4 w-4 mr-1 text-amber-600" />
                      Magic Edit™
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1 min-w-[90px]">
                      Templates
                    </Button>
                    {/* Soundscapes button with popover */}
                    <div className="relative flex-1 min-w-[90px]">
                      <Button
                        size="sm"
                        variant="outline"
                        className="w-full"
                        onClick={() => setShowSoundPopover((v) => !v)}
                        type="button"
                      >
                        <Volume2 className="h-4 w-4 mr-1" />
                        Soundscapes
                      </Button>
                      {showSoundPopover && (
                        <div className="absolute left-0 mt-2 w-48 bg-background border border-border rounded-lg shadow-xl z-50 p-2">
                          <div className="text-xs font-medium mb-2 px-2 text-popover-foreground">Soundscapes</div>
                          <div className="space-y-1">
                            {['Rainfall', 'Coffee Shop', 'Forest Night'].map(sound => (
                              <Button key={sound} variant="ghost" size="sm" className="w-full justify-start text-xs h-7 text-popover-foreground hover:bg-accent">
                                {sound}
                              </Button>
                            ))}
                            <Button variant="ghost" size="sm" className="w-full justify-start text-xs h-7 text-muted-foreground hover:bg-accent">
                              Ocean Waves (Premium)
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
            
            <Separator className="my-4" />
            
            <div className="flex items-center mb-4">
              <Sparkles className="h-4 w-4 mr-2 text-amber-500" />
              <h3 className="font-medium text-sm">Chapters</h3>
            </div>
            
            <ScrollArea className="flex-1 -mx-4 px-4">
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="chapters" isDropDisabled={false}>
                  {(provided) => (
                    <div
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      className="space-y-1"
                    >
                      {chapters.map((chapter, index) => (
                        <Draggable 
                          key={chapter.id} 
                          draggableId={chapter.id} 
                          index={index}
                        >
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              className={`rounded-md flex items-center p-2 group ${
                                activeChapterId === chapter.id 
                                  ? 'bg-accent/50 text-accent-foreground' 
                                  : 'hover:bg-muted'
                              }`}
                            >
                              <div {...provided.dragHandleProps} className="mr-2 opacity-50 hover:opacity-100">
                                <GripVertical className="h-4 w-4" />
                              </div>
                              
                              <div
                                className="flex-1 min-w-0 cursor-pointer"
                                onClick={() => handleSelectChapter(chapter)}
                              >
                                <div className="text-sm font-medium truncate">{chapter.title}</div>
                                <div className="text-xs text-muted-foreground mt-0.5 flex items-center gap-1">
                                  <span>{chapter.word_count?.toLocaleString() ?? 0} words</span>
                                  {chapter.chapter_order && 
                                    <span className="px-1.5 py-0.5 rounded-full bg-primary/10 text-primary text-[0.65rem]">
                                      Ch. {chapter.chapter_order}
                                    </span>
                                  }
                                </div>
                              </div>
                              
                              <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-7 w-7"
                                  onClick={() => {
                                    setChapterToRename(chapter);
                                    setNewChapterTitle(chapter.title);
                                  }}
                                >
                                  <Pencil className="h-3.5 w-3.5" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-7 w-7 text-destructive"
                                  onClick={() => setChapterToDelete(chapter)}
                                >
                                  <Trash2 className="h-3.5 w-3.5" />
                                </Button>
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            </ScrollArea>
            
            <div className="pt-4 mt-4">
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={handleAddChapter}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <PlusCircle className="h-4 w-4 mr-2" />
                )}
                Add New Chapter
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>

      {/* Rename Chapter Dialog */}
      <AlertDialog 
        open={!!chapterToRename} 
        onOpenChange={(open) => {
          if (!open) setChapterToRename(null);
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Rename Chapter</AlertDialogTitle>
            <AlertDialogDescription>
              Enter a new title for this chapter
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="py-4">
            <Input
              value={newChapterTitle}
              onChange={(e) => setNewChapterTitle(e.target.value)}
              className="mb-4"
              placeholder="Chapter title"
              disabled={isLoading}
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleRenameChapter();
              }}
              disabled={!newChapterTitle.trim() || isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Changes'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Chapter Dialog */}
      <AlertDialog 
        open={!!chapterToDelete} 
        onOpenChange={(open) => {
          if (!open) setChapterToDelete(null);
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Chapter</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{chapterToDelete?.title}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleDeleteChapter();
              }}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
