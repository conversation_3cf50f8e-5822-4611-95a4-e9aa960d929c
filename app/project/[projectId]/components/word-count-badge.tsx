'use client';

import { useEffect, useState } from 'react';
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { BookText, Target } from "lucide-react";
import { Progress } from "@/components/ui/progress";

interface WordCountBadgeProps {
  content: string;
  wordGoal?: number;
  totalWords?: number;
  className?: string;
}

export function WordCountBadge({ content, wordGoal, totalWords, className = '' }: WordCountBadgeProps) {
  // Calculate word count from content (current chapter)
  const chapterWordCount = content ? content.trim().split(/\s+/).filter(Boolean).length : 0;
  const [animatedCount, setAnimatedCount] = useState(chapterWordCount);
  const [progress, setProgress] = useState(0);

  // Use either provided total or just the current chapter count
  const projectWordCount = totalWords !== undefined ? totalWords : chapterWordCount;

  // Listen for word-count-update events
  useEffect(() => {
    const handleWordCountUpdate = () => {
      console.log('[WordCountBadge] Received word-count-update event, forcing refresh');
      // Force immediate update to the current word count
      setAnimatedCount(chapterWordCount);
    };

    // Add event listener
    window.addEventListener('word-count-update', handleWordCountUpdate);

    // Cleanup
    return () => {
      window.removeEventListener('word-count-update', handleWordCountUpdate);
    };
  }, [chapterWordCount]);

  // Animate count on change
  useEffect(() => {
    // If difference is large, don't animate
    if (Math.abs(chapterWordCount - animatedCount) > 100) {
      setAnimatedCount(chapterWordCount);
      return;
    }

    // Small difference, animate
    if (chapterWordCount !== animatedCount) {
      const interval = setInterval(() => {
        setAnimatedCount(prev => {
          if (prev < chapterWordCount) return prev + 1;
          if (prev > chapterWordCount) return prev - 1;
          clearInterval(interval);
          return prev;
        });
      }, 20);

      return () => clearInterval(interval);
    }
  }, [chapterWordCount, animatedCount]);

  // Calculate progress percentage
  useEffect(() => {
    if (wordGoal && wordGoal > 0) {
      const newProgress = Math.min(Math.round((projectWordCount / wordGoal) * 100), 100);
      setProgress(newProgress);
    } else {
      setProgress(0);
    }
  }, [projectWordCount, wordGoal]);

  // Format count with thousand separators
  const formattedChapterCount = animatedCount.toLocaleString();
  const formattedProjectCount = projectWordCount.toLocaleString();
  const formattedTarget = wordGoal?.toLocaleString();

  // Calculate estimated pages (average 250 words per page for standard manuscript)
  const estimatedChapterPages = Math.ceil(chapterWordCount / 250);
  const estimatedProjectPages = Math.ceil(projectWordCount / 250);

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <Badge variant="outline" className={`px-3 py-1.5 flex items-center gap-2 font-medium shadow-sm ${className}`}>
            <BookText className="h-4 w-4 text-primary" />
            <div className="flex items-center gap-1.5">
              <span className="font-semibold">{totalWords !== undefined ? formattedProjectCount : formattedChapterCount}</span>
              {wordGoal && wordGoal > 0 && (
                <>
                  <span className="text-muted-foreground">/</span>
                  <span className="text-muted-foreground">{formattedTarget}</span>
                  <div className="ml-1.5 px-1.5 py-0.5 rounded bg-primary/10 text-primary text-xs font-medium">
                    {progress}%
                  </div>
                </>
              )}
            </div>
          </Badge>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="p-4 max-w-xs">
          <div className="space-y-2">
            <p className="font-medium">Word Count Statistics</p>

            <div className="text-sm space-y-1 text-muted-foreground">
              {totalWords !== undefined && (
                <>
                  <p>Project Total: {formattedProjectCount} words</p>
                  <p>Current Chapter: {formattedChapterCount} words</p>
                </>
              )}
              {!totalWords && (
                <p>Current Chapter: {formattedChapterCount} words</p>
              )}

              {wordGoal && wordGoal > 0 && (
                <>
                  <p>Target: {formattedTarget} words</p>
                  <div className="pt-1">
                    <Progress value={progress} className="h-1.5" />
                  </div>
                </>
              )}

              <p className="pt-1">Estimated Pages:
                {totalWords !== undefined ?
                  ` ~${estimatedProjectPages} pages (total) / ~${estimatedChapterPages} pages (chapter)` :
                  ` ~${estimatedChapterPages} pages`
                }
              </p>
              <p className="text-xs opacity-70">(Based on 250 words per page)</p>
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
