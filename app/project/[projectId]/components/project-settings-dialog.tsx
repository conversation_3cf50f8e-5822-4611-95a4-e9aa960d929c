'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { Settings, Upload, Loader2, Target } from 'lucide-react';
import { toast } from 'sonner';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { GoldProgress } from '@/components/ui/gold-progress';
import { updateProjectCover, updateProjectGoal } from '@/lib/supabase/database';
import { useProjectProgress } from '@/hooks/use-project-progress-fixed';

interface ProjectSettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: string;
  project: {
    id: string;
    title: string;
    author: string;
    cover_image_url?: string | null;
    word_goal?: number;
  };
  onProjectUpdate: (updatedProject: any) => void;
}

export function ProjectSettingsDialog({
  open,
  onOpenChange,
  projectId,
  project,
  onProjectUpdate,
}: ProjectSettingsDialogProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(project.cover_image_url || null);
  const [wordGoal, setWordGoal] = useState<string>(project.word_goal?.toString() || '50000');
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Get project progress data using the hook
  const { projectProgress, loading: progressLoading } = useProjectProgress([project], project.id);
  
  useEffect(() => {
    // Reset state when dialog opens
    if (open) {
      setSelectedFile(null);
      setPreviewUrl(project.cover_image_url || null);
      setWordGoal(project.word_goal?.toString() || '50000');
    }
  }, [open, project]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image file size must be less than 5MB');
      return;
    }

    setSelectedFile(file);

    // Create preview URL
    const objectUrl = URL.createObjectURL(file);
    setPreviewUrl(objectUrl);

    // Clean up the object URL when component unmounts
    return () => URL.revokeObjectURL(objectUrl);
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };
  
  // Get progress data for the current project
  const getProgressData = () => {
    // Default values in case the hook hasn't loaded yet
    const defaultProgress = {
      calculated_word_count: 0,
      goal: parseInt(wordGoal) || project.word_goal || 50000,
      progress_percentage: 0
    };
    
    return projectProgress[project.id] || defaultProgress;
  };

  const handleSaveChanges = async () => {
    setIsSaving(true);
    let hasChanges = false;
    let updatedProject = { ...project };
    
    try {
      // Handle word goal update if changed
      const goalValue = parseInt(wordGoal);
      if (goalValue > 0 && goalValue !== project.word_goal) {
        const goalSuccess = await updateProjectGoal(projectId, goalValue);
        if (goalSuccess) {
          updatedProject.goal = goalValue;
          hasChanges = true;
          toast.success('Word goal updated successfully');
        } else {
          toast.error('Failed to update word goal');
        }
      }
      
      // Handle cover image update if selected
      if (selectedFile) {
        setIsUploading(true);
        
        const { newUrl, error } = await updateProjectCover(
          projectId,
          selectedFile,
          project.cover_image_url || null
        );

        if (error) {
          throw error;
        }

        updatedProject.cover_image_url = newUrl;
        hasChanges = true;
        toast.success('Cover image updated successfully');
      }
      
      // Update local state if any changes were made
      if (hasChanges) {
        onProjectUpdate(updatedProject);
      }
      
      onOpenChange(false);
    } catch (error: any) {
      console.error('Error updating project settings:', error);
      toast.error('Failed to update project settings: ' + (error.message || 'Unknown error'));
    } finally {
      setIsUploading(false);
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Project Settings</DialogTitle>
          <DialogDescription>
            Update your project details including cover image and word goal.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="space-y-2">
            <Label htmlFor="project-title">Project Title</Label>
            <Input
              id="project-title"
              value={project.title}
              readOnly
              className="bg-muted/50"
            />
          </div>
          
          {/* Word Count Progress */}
          <div className="space-y-3">
            <Label>Project Progress</Label>
            <div className="p-4 rounded-md bg-muted/30 space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Current Word Count:</span>
                <span className="font-medium">{getProgressData().calculated_word_count.toLocaleString()}</span>
              </div>
              
              <GoldProgress value={getProgressData().progress_percentage} className="h-2.5" />
              
              <div className="flex justify-between text-xs">
                <span className="text-muted-foreground">0%</span>
                <span className="text-muted-foreground">
                  {getProgressData().progress_percentage}%
                </span>
                <span className="text-muted-foreground">100%</span>
              </div>
            </div>
          </div>
          
          {/* Word Goal Setting */}
          <div className="space-y-2">
            <Label htmlFor="word-goal">Target Word Count</Label>
            <div className="relative">
              <Target className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                id="word-goal"
                type="number"
                placeholder="50000"
                value={wordGoal}
                onChange={(e) => setWordGoal(e.target.value)}
                className="bg-muted/50 pl-10"
                min="1"
              />
            </div>
            <p className="text-xs text-muted-foreground">
              Setting a realistic word goal helps track your writing progress.
            </p>
          </div>

          <div className="space-y-4">
            <Label>Cover Image</Label>

            <div className="flex flex-col items-center space-y-4">
              {/* Vertical Cover Preview Container */}
              <div className="aspect-[10/16] w-32 rounded-md overflow-hidden border border-border bg-muted shadow-sm relative">
                {previewUrl ? (
                  <Image
                    src={previewUrl}
                    alt="Book Cover"
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="absolute inset-0 flex flex-col items-center justify-center p-2 text-center">
                    <p className="text-xs text-muted-foreground">No cover image</p>
                  </div>
                )}
              </div>

              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept="image/*"
                onChange={handleFileChange}
              />

              <Button 
                type="button" 
                variant="outline" 
                onClick={triggerFileInput}
                className="w-full max-w-[160px]"
                size="sm"
              >
                <Upload className="h-3.5 w-3.5 mr-1.5" />
                {previewUrl ? 'Replace Cover' : 'Upload Cover'}
              </Button>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isUploading || isSaving}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSaveChanges}
            disabled={isUploading || isSaving}
          >
            {isUploading || isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              'Save Changes'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export function ProjectSettingsButton({
  onClick,
  className,
}: {
  onClick: () => void;
  className?: string;
}) {
  return (
    <Button
      variant="outline"
      size="icon"
      onClick={onClick}
      className={className}
    >
      <Settings className="h-5 w-5 text-primary" />
    </Button>
  );
}
