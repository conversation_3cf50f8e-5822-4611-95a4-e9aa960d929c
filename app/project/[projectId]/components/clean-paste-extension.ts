/**
 * clean-paste-extension.ts
 * A TipTap extension to clean pasted content
 */

import { Extension } from '@tiptap/core';
import { Plugin, PluginKey } from '@tiptap/pm/state';
import { Fragment, Slice as ProseMirrorSliceModel } from 'prosemirror-model'; // Added import

/**
 * CleanPastePlugin removes unwanted formatting from pasted content
 */
export const CleanPasteExtension = Extension.create({
  name: 'cleanPaste',

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('cleanPaste'),
        props: {
          // This function runs before the paste is handled by the editor
          transformPasted: (slice, view) => { // Added 'view' parameter
            // Get all nodes from the slice
            const oldFragment = slice.content;
            const cleanNodes = [] as any[]; // Explicitly type as any[] or correct Node type

            // Process all nodes in the fragment
            oldFragment.forEach(node => {
              let modifiedNode = node;
              // Strip unwanted attributes from node marks
              if (node.marks && node.marks.length) {
                const cleanMarks = node.marks.filter(mark => {
                  // Keep only certain marks and remove others like font-family, color, etc.
                  return ['italic', 'bold', 'underline', 'link'].includes(mark.type.name);
                });
                
                // Create a new node with clean marks if marks changed
                if (cleanMarks.length !== node.marks.length) {
                  // Use the schema from the view to create the node
                  modifiedNode = view.state.schema.node(node.type.name, node.attrs, node.content, cleanMarks);
                } else {
                  // If marks are already clean, no need to create a new node instance here
                  // unless other transformations are done. For now, keep original.
                }
              }
              cleanNodes.push(modifiedNode);
            });
            
            // Create a new Fragment from the cleaned nodes using the imported constructor
            const newFragment = Fragment.fromArray(cleanNodes);

            // Return a new slice with cleaned content using the imported constructor
            return new ProseMirrorSliceModel(newFragment, slice.openStart, slice.openEnd);
          },
          
          // This function runs after the paste is handled by the editor
          handlePaste: (view, event, slice) => {
            // Additional cleanup after paste if needed
            
            // Let the default paste handler work
            return false;
          },
        },
      }),
    ];
  },
});
