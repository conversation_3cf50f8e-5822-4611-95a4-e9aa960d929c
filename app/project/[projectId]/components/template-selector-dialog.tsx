'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { supabase } from '@/lib/supabase/client';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface TemplateSelectorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: string;
}

interface Template {
  id: string;
  name: string;
  description: string;
  previewClass: string;
}

const TEMPLATES: Template[] = [
  {
    id: 'default',
    name: 'Classic',
    description: 'Traditional serif font on white background',
    previewClass: 'bg-white text-black',
  },
  {
    id: 'dark',
    name: 'Dark Mode',
    description: 'White text on dark background',
    previewClass: 'bg-gray-900 text-white',
  },
  {
    id: 'sepia',
    name: '<PERSON><PERSON>',
    description: 'Classic sepia tone for reduced eye strain',
    previewClass: 'bg-[#f8f1e3] text-[#5f4b32]',
  },
  {
    id: 'modern',
    name: 'Modern',
    description: 'Clean sans-serif typography',
    previewClass: 'bg-gray-50 text-gray-800 font-sans',
  },
  {
    id: 'typewriter',
    name: 'Typewriter',
    description: 'Monospace font for a typewriter feel',
    previewClass: 'bg-gray-100 text-gray-900 font-mono',
  },
  {
    id: 'vintage',
    name: 'Vintage',
    description: 'Aged paper look',
    previewClass: 'bg-[#f0e6d2] text-[#5c4b37]',
  },
];

export function TemplateSelectorDialog({ 
  open, 
  onOpenChange, 
  projectId 
}: TemplateSelectorDialogProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('default');
  const [isLoading, setIsLoading] = useState(false);

  // Fetch the current template when the dialog opens
  useEffect(() => {
    if (open) {
      const fetchTemplate = async () => {
        try {
          const { data, error } = await supabase
            .from('projects')
            .select('template_name')
            .eq('id', projectId)
            .single();

          if (error) throw error;
          if (data?.template_name) {
            setSelectedTemplate(data.template_name);
          }
        } catch (error) {
          console.error('Error fetching template:', error);
        }
      };

      fetchTemplate();
    }
  }, [open, projectId]);

  const applyTemplate = async (templateId: string) => {
    if (templateId === selectedTemplate) return;
    
    setIsLoading(true);
    
    try {
      const { error } = await supabase
        .from('projects')
        .update({ template_name: templateId })
        .eq('id', projectId);

      if (error) throw error;
      
      setSelectedTemplate(templateId);
      toast.success(`Template changed to ${TEMPLATES.find(t => t.id === templateId)?.name}`);
      
      // Close the dialog after a short delay to show the selection
      setTimeout(() => onOpenChange(false), 500);
    } catch (error) {
      console.error('Error changing template:', error);
      toast.error('Failed to change template');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Choose a Template</DialogTitle>
          <DialogDescription>
            Select a visual theme for your writing environment
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 py-4">
          {TEMPLATES.map((template) => (
            <Card 
              key={template.id}
              className={cn(
                "overflow-hidden cursor-pointer transition-all",
                selectedTemplate === template.id ? "ring-2 ring-primary" : "hover:ring-1 hover:ring-muted-foreground"
              )}
              onClick={() => applyTemplate(template.id)}
            >
              <div 
                className={cn(
                  "h-24 p-3 flex items-end font-serif text-sm",
                  template.previewClass
                )}
              >
                <p>The quick brown fox jumps over the lazy dog.</p>
              </div>
              <CardContent className="p-3">
                <h3 className="font-medium text-sm">{template.name}</h3>
                <p className="text-xs text-muted-foreground mt-1">{template.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}