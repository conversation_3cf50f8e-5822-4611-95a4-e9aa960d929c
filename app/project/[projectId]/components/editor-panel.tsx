'use client';

import React, { useCallback, useEffect, useState, useRef, useMemo, useImperativeHandle, forwardRef } from 'react';
import { useE<PERSON><PERSON>, EditorContent, Editor, Mark } from '@tiptap/react'; // Added Mark
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import { Loader2, CheckCircle, Check, X, RotateCcw, Sparkles } from 'lucide-react'; // Removed Wand2 as it might be unused after dialog removal
import { toast } from 'sonner';
import { Chapter } from '../page';
import { debounce, cn } from '@/lib/utils';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { CleanPasteExtension } from './clean-paste-extension';
import './editor-styles.css';
// MagicEditDialog import removed as it's handled by the parent
// import { MagicEditCreditsDisplay } from '@/components/magic-edit-credits-display'; // Likely also handled by parent if part of dialog

interface EditorPanelProps {
  chapter: Chapter;
  onContentChange: (htmlContent: string, wordCount: number) => void;
  onSubtitleChange?: (subtitle: string | null) => void;
  zenMode: boolean;
  setZenMode: React.Dispatch<React.SetStateAction<boolean>>;
}

export interface EditorPanelRef {
  getSelectedContentForRewrite: () => { html: string; text: string; from: number; to: number } | null;
  // New function to get unedited chunk
  getNextUneditedChunk: () => { html: string; text: string; from: number; to: number; wordCount: number } | null;
  storeOriginalSelection: () => void;
  updateScrapPaper: (htmlContent: string) => void;
  switchToScrapPaperTab: () => void;
  replaceMainContentWithScrapPaper: () => void;
  triggerSave: () => void;
  // New functions for adjustable Magic Edit selection
  highlightMagicEditSelection: (from: number, to: number, wordCount: number) => void;
  clearMagicEditSelection: () => void;
  getCurrentSelection: () => { html: string; text: string; from: number; to: number; wordCount: number } | null;
  // Auto-highlight and set selection for Magic Edit
  autoSelectForMagicEdit: () => { html: string; text: string; from: number; to: number; wordCount: number } | null;
}

// Define the custom mark for Magic Edited text
const MagicEditedMark = Mark.create({
  name: 'magicEdited',

  addOptions() {
    return {
      HTMLAttributes: {
        'data-magic-edited': 'true',
        // class: 'magic-edited-text', // Optional: for styling/debugging
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'span[data-magic-edited="true"]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ['span', HTMLAttributes, 0];
  },

  addCommands() {
    return {
      setMagicEdited: () => ({ commands }: { commands: any }) => { // Add explicit any
        return commands.setMark(this.type);
      },
      toggleMagicEdited: () => ({ commands }: { commands: any }) => { // Add explicit any
        return commands.toggleMark(this.type);
      },
      unsetMagicEdited: () => ({ commands }: { commands: any }) => { // Add explicit any
        return commands.unsetMark(this.type);
      },
    } as any;
  },
});

// Define the preview selection mark for Magic Edit
const MagicEditPreviewMark = Mark.create({
  name: 'magicEditPreview',

  addOptions() {
    return {
      HTMLAttributes: {
        'data-magic-edit-preview': 'true',
        class: 'magic-edit-preview-selection',
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'span[data-magic-edit-preview="true"]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ['span', HTMLAttributes, 0];
  },

  addCommands() {
    return {
      setMagicEditPreview: () => ({ commands }: { commands: any }) => {
        return commands.setMark(this.type);
      },
      unsetMagicEditPreview: () => ({ commands }: { commands: any }) => {
        return commands.unsetMark(this.type);
      },
    } as any;
  },
});

// Simplified internal word count utility
const countWordsInternal = (text: string): number => {
  if (!text) return 0;
  return text.trim().split(/\s+/).filter(Boolean).length;
};

export const EditorPanel = forwardRef<EditorPanelRef, EditorPanelProps>(({
  chapter,
  onContentChange,
  onSubtitleChange,
  zenMode,
  setZenMode,
}, ref) => {
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved'>('idle');
  const [subtitle, setSubtitle] = useState<string | null>(chapter.subtitle || null);
  const [activeUITab, setActiveUITab] = useState<'main-draft' | 'scrap-paper'>('main-draft');
  const [scrapPaperContent, setScrapPaperContent] = useState<string>('');

  // Magic Edit state related to the internal dialog is removed.
  // States like isGeneratingMagicEdit, magicEditSuggestion, showMagicEditPanel are managed by the parent (WritingDeskPage).

  const editorContainerRef = useRef<HTMLDivElement>(null);
  const originalSelectionRef = useRef<{ from: number; to: number; htmlSlice: string } | null>(null);

  const debouncedSaveHandler = useCallback(
    debounce((editorInstance: Editor) => {
      setSaveStatus('saving');
      const html = editorInstance.getHTML();
      const wordCount = countWordsInternal(editorInstance.getText());
      onContentChange(html, wordCount);
      setTimeout(() => {
        setSaveStatus('saved');
        setTimeout(() => setSaveStatus('idle'), 1500);
      }, 300);
    }, 1000),
    [onContentChange]
  );

  const editor = useEditor({
    extensions: [
      StarterKit.configure({}),
      Placeholder.configure({ placeholder: 'Begin your masterpiece here...' }),
      CleanPasteExtension,
      MagicEditedMark, // Add the custom mark here
      MagicEditPreviewMark, // Add the preview selection mark
    ],
    content: chapter.content || '<p></p>',
    onUpdate: ({ editor: currentEditor }) => {
      debouncedSaveHandler(currentEditor);
    },
    onBlur: ({ editor: currentEditor }) => {
      setSaveStatus('saving'); // Always show saving on blur if changes were made
      const html = currentEditor.getHTML();
      const wordCount = countWordsInternal(currentEditor.getText());
      onContentChange(html, wordCount);
      setTimeout(() => {
        setSaveStatus('saved');
        setTimeout(() => setSaveStatus('idle'), 1500);
      }, 300);
    },
    editorProps: {
      attributes: {
        class: 'prose prose-base sm:prose-lg dark:prose-invert focus:outline-none max-w-none editor-styling-area', // Adjusted prose size
      },
    },
  });

  const scrapPaperEditor = useEditor({
    extensions: [StarterKit.configure(), Placeholder.configure({ placeholder: 'AI generated text will appear here.'})],
    content: scrapPaperContent,
    editable: false,
    editorProps: {
      attributes: { class: 'prose prose-base sm:prose-lg dark:prose-invert focus:outline-none max-w-none editor-styling-area' },
    },
  });

  useEffect(() => {
    if (editor && chapter) {
      const currentEditorHtml = editor.getHTML();
      if (currentEditorHtml !== chapter.content) {
        editor.commands.setContent(chapter.content || '<p></p>', false);
      }
      setSubtitle(chapter.subtitle || null);
      if (activeUITab === 'scrap-paper' && !scrapPaperContent) { // If scrap paper tab is active but content became empty
          setActiveUITab('main-draft');
      }
    }
  }, [editor, chapter.id, chapter.content, chapter.subtitle]);

  useEffect(() => {
    if (scrapPaperEditor) {
      scrapPaperEditor.commands.setContent(scrapPaperContent || '<p></p>');
    }
  }, [scrapPaperEditor, scrapPaperContent]);

  const [showFloatingMetrics, setShowFloatingMetrics] = useState(false);
  const inactivityTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [zenHover, setZenHover] = useState(false);

  useEffect(() => {
    if (zenMode) {
      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === 'Escape') setZenMode(false);
      };
      window.addEventListener('keydown', handleEscape);
      return () => window.removeEventListener('keydown', handleEscape);
    }
  }, [zenMode, setZenMode]);

  useEffect(() => {
    if (!zenMode) return;
    const handleMouseMove = (e: MouseEvent) => {
      setZenHover(e.clientY < 32);
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [zenMode]);

  useEffect(() => {
    if (zenMode) return;
    const resetTimer = () => {
      if (inactivityTimerRef.current) clearTimeout(inactivityTimerRef.current);
      setShowFloatingMetrics(false);
      inactivityTimerRef.current = setTimeout(() => {
        setShowFloatingMetrics(true);
      }, 5000);
    };
    const hideMetrics = () => {
      setShowFloatingMetrics(false);
      if (inactivityTimerRef.current) clearTimeout(inactivityTimerRef.current);
      inactivityTimerRef.current = setTimeout(() => {
        setShowFloatingMetrics(true);
      }, 5000);
    };

    document.addEventListener('mousemove', resetTimer);
    document.addEventListener('keydown', hideMetrics);
    resetTimer();

    return () => {
      document.removeEventListener('mousemove', resetTimer);
      document.removeEventListener('keydown', hideMetrics);
      if (inactivityTimerRef.current) clearTimeout(inactivityTimerRef.current);
    };
  }, [zenMode]);

  const handleSubtitleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSubtitle = e.target.value || null;
    setSubtitle(newSubtitle);
    if (onSubtitleChange) {
      setSaveStatus('saving');
      onSubtitleChange(newSubtitle);
      setTimeout(() => {
        setSaveStatus('saved');
        setTimeout(() => setSaveStatus('idle'), 1500);
      }, 300);
    }
  };

  // handleGenerateMagicEdit and handleAcceptMagicEdit are removed as these are handled by WritingDeskPage.
  // The MagicEditDialog instance that used these is also removed.

  useImperativeHandle(ref, () => ({
    getNextUneditedChunk: () => {
      if (!editor || editor.isDestroyed) {
        console.error('[EditorPanel] getNextUneditedChunk: Editor not available or destroyed.');
        return null;
      }
      console.log('[EditorPanel] getNextUneditedChunk: Initiating search. Full editor document JSON:', editor.state.doc.toJSON());

      const MAX_WORDS = 5000;
      let collectedPlainText = "";
      let currentWordCount = 0;
      let chunkStartPos = -1;
      let chunkEndPos = -1;

      editor.state.doc.descendants((node, pos) => {
        if (currentWordCount >= MAX_WORDS) return false;

        if (node.isText) {
          const isNodeEdited = node.marks.some(mark => mark.type.name === MagicEditedMark.name);
          console.log(`[EditorPanel] Text node at ${pos}: "${node.text?.substring(0,30)}...", Edited: ${isNodeEdited}, WordCount: ${countWordsInternal(node.text || "")}`);

          if (!isNodeEdited) {
            if (chunkStartPos === -1) {
              chunkStartPos = pos;
              console.log(`[EditorPanel] Starting unedited chunk at position ${chunkStartPos}`);
            }

            let textFromNode = node.text || "";
            const wordsInThisNode = countWordsInternal(textFromNode);

            if (currentWordCount + wordsInThisNode > MAX_WORDS) {
              const remainingWordsToTake = MAX_WORDS - currentWordCount;
              let wordsTakenFromThisNode = 0;
              let partialNodeText = "";
              const wordParts = textFromNode.split(/(\s+)/);

              for (const part of wordParts) {
                if (part.trim() !== "") {
                  if (wordsTakenFromThisNode < remainingWordsToTake) {
                    partialNodeText += part;
                    wordsTakenFromThisNode++;
                  } else {
                    break;
                  }
                } else {
                  partialNodeText += part;
                }
              }
              textFromNode = partialNodeText;
              console.log(`[EditorPanel] Taking partial text from node: "${textFromNode}", words: ${wordsTakenFromThisNode}`);
            }

            collectedPlainText += textFromNode;
            currentWordCount = countWordsInternal(collectedPlainText);
            chunkEndPos = pos + textFromNode.length;

            console.log(`[EditorPanel] Accumulated: ${currentWordCount} words. Current chunkEndPos: ${chunkEndPos}. Text: "${collectedPlainText.substring(0,100)}..."`);

            if (currentWordCount >= MAX_WORDS) {
              console.log('[EditorPanel] MAX_WORDS reached.');
              return false;
            }
          } else {
            if (chunkStartPos !== -1) {
              console.log(`[EditorPanel] Hit edited text at ${pos} after starting collection. Ending current unedited chunk.`);
              return false;
            }
          }
        } else if (node.isBlock && chunkStartPos !== -1 && collectedPlainText.length > 0 && !collectedPlainText.endsWith(" ") && !collectedPlainText.endsWith("\n")) {
            collectedPlainText += " ";
        }
        return true;
      });

      if (chunkStartPos === -1 || collectedPlainText.trim() === "") {
        console.log('[EditorPanel] No unedited content found or collected text is empty.');
        toast.info("No unedited content found to Magic Edit™.");
        return null;
      }

      chunkEndPos = Math.max(chunkEndPos, chunkStartPos + collectedPlainText.trim().length);
      chunkEndPos = Math.min(chunkEndPos, editor.state.doc.content.size);

      if (chunkEndPos <= chunkStartPos && collectedPlainText.trim() !== "") {
          chunkEndPos = chunkStartPos + collectedPlainText.trim().length;
          chunkEndPos = Math.min(chunkEndPos, editor.state.doc.content.size);
          console.warn(`[EditorPanel] Adjusted chunkEndPos to ${chunkEndPos} based on collected text length.`);
      }

      let finalHtml = "";
      let finalPlainText = "";
      let finalWordCount = 0;

      if (chunkStartPos !== -1 && chunkEndPos > chunkStartPos) {
          const finalSlice = editor.state.doc.slice(chunkStartPos, chunkEndPos);

          // Diagnostic logging
          console.log('[EditorPanel] About to create temp editor. Slice details:');
          console.log('[EditorPanel] Slice JSON:', finalSlice.content.toJSON());
          console.log('[EditorPanel] Slice size:', finalSlice.content.size);
          console.log('[EditorPanel] Main editor schema nodes:', Object.keys(editor.schema.nodes));

          // Create temp editor with main editor's extensions and schema validation
          // Create fresh extension instances to avoid plugin conflicts
          const tempEditorExtensions = [
            StarterKit.configure({
              history: false,
              heading: false,
              codeBlock: false, // Simplify temp editor config
            }),
            Placeholder.configure({
              placeholder: 'Temp editor content...',
              emptyEditorClass: 'is-editor-empty'
            }),
            CleanPasteExtension,
            MagicEditedMark,
          ];

          const tempEditor = new Editor({
            extensions: tempEditorExtensions,
            editorProps: {
              attributes: {
                'data-temp-editor': 'true',
                'data-immediately-render': 'false'
              }
            },
            onBeforeCreate: ({ editor: tempEd }) => {
              console.log('[EditorPanel] Temp editor schema nodes:', Object.keys(tempEd.schema.nodes));
            }
          });
          try {
            // Create proper document structure from the slice
            const docNode = tempEditor.schema.nodes.doc.create(
              null,
              finalSlice.content
            );
            tempEditor.commands.setContent(docNode.toJSON());
            finalHtml = tempEditor.getHTML();
          } catch (error) {
            console.error('[EditorPanel] Structured HTML generation failed:', error);
            console.log('[EditorPanel] Falling back to text extraction');
            const textContent = finalSlice.content.textBetween(0, finalSlice.content.size, "\n");
            finalHtml = `<div class="prose">${textContent}</div>`;
          } finally {
            tempEditor.destroy();
          }

          finalPlainText = editor.state.doc.textBetween(chunkStartPos, chunkEndPos, "\n\n");
          finalWordCount = countWordsInternal(finalPlainText);

          editor.commands.setTextSelection({ from: chunkStartPos, to: chunkEndPos });
          console.log(`[EditorPanel] Final chunk selected: ${finalWordCount} words. From ${chunkStartPos} to ${chunkEndPos}.`);
      } else {
          console.log('[EditorPanel] Invalid range for slicing or no text collected. Returning null.');
          toast.info("Could not select a valid text chunk for Magic Edit™.");
          return null;
      }

      if (finalWordCount === 0 && finalPlainText.trim() !== "") {
        finalWordCount = finalPlainText.trim().split(/\s+/).filter(Boolean).length;
        console.warn(`[EditorPanel] Recalculated word count for non-empty text: ${finalWordCount}`);
      }

      if (finalWordCount === 0) {
        console.log('[EditorPanel] Final processed chunk has 0 words despite collecting text.');
        toast.info("No unedited content found to Magic Edit™.");
        return null;
      }

      return {
        html: finalHtml,
        text: finalPlainText,
        from: chunkStartPos,
        to: chunkEndPos,
        wordCount: finalWordCount,
      };
    },
    getSelectedContentForRewrite: () => {
      if (!editor || editor.isDestroyed) return null;
      const { from, to, empty } = editor.state.selection;

      if (empty) {
        const MIN_WORDS = 500;
        const $anchor = editor.state.selection.$anchor;
        let node = $anchor.node($anchor.depth);
        let nodeStartPos = $anchor.start($anchor.depth);
        let currentPos = $anchor.end($anchor.depth);

        if (!node || !node.isTextblock) {
            editor.state.doc.descendants((descNode, pos) => {
                if (descNode.isTextblock) {
                    node = descNode;
                    nodeStartPos = pos;
                    currentPos = pos + descNode.nodeSize;
                    return false;
                }
                return true;
            });
            if (!node) return null;
        }

        let combinedContent = node.textContent;
        let combinedHTML = '';
        const tempEditorForHtml = new Editor({ content: node.toJSON(), extensions: [StarterKit] });
        combinedHTML = tempEditorForHtml.getHTML();
        tempEditorForHtml.destroy();

        let wordCount = countWordsInternal(combinedContent);
        let attempts = 0;

        while (wordCount < MIN_WORDS && attempts < 10) {
          attempts++;
          try {
            if (currentPos >= editor.state.doc.content.size -1) break;
            const nextNodePos = editor.state.doc.resolve(currentPos + 1);
            if (nextNodePos.depth === 0 && nextNodePos.parent.type.name === 'doc' && !nextNodePos.parent.maybeChild(nextNodePos.index())) break;

            const nextNodeToProcess = nextNodePos.node(nextNodePos.depth);
            if (!nextNodeToProcess || !nextNodeToProcess.isTextblock || !nextNodeToProcess.textContent) {
                let foundNextTextBlock = false;
                editor.state.doc.nodesBetween(currentPos + 1, editor.state.doc.content.size, (descNode, posInBetween) => {
                    if (descNode.isTextblock && descNode.textContent) {
                        combinedContent += ' ' + descNode.textContent;
                        const tempEditorNode = new Editor({ content: descNode.toJSON(), extensions: [StarterKit] });
                        combinedHTML += tempEditorNode.getHTML();
                        tempEditorNode.destroy();
                        currentPos = posInBetween + descNode.nodeSize;
                        wordCount = countWordsInternal(combinedContent);
                        foundNextTextBlock = true;
                        return false;
                    }
                    return true;
                });
                if (!foundNextTextBlock) break;
                continue;
            }

            combinedContent += ' ' + nextNodeToProcess.textContent;
            const tempEditorNode = new Editor({ content: nextNodeToProcess.toJSON(), extensions: [StarterKit] });
            combinedHTML += tempEditorNode.getHTML();
            tempEditorNode.destroy();
            currentPos = nextNodePos.end(nextNodePos.depth);
            wordCount = countWordsInternal(combinedContent);
          } catch (error) {
            console.log("Error while gathering paragraphs for Magic Edit (getSelectedContentForRewrite):", error);
            break;
          }
        }
        console.log(`Selected ${wordCount} words for Magic Edit (getSelectedContentForRewrite) from ${attempts + 1} paragraphs`);
        return { html: combinedHTML, text: combinedContent, from: nodeStartPos, to: currentPos };
      }

      const text = editor.state.doc.textBetween(from, to, '\n\n');
      let selectedHtml = '';
      const slice = editor.state.selection.content();
      const tempEditorForSlice = new Editor({ content: slice.toJSON(), extensions: [StarterKit] });
      selectedHtml = tempEditorForSlice.getHTML();
      tempEditorForSlice.destroy();
      const selectedWordCount = countWordsInternal(text);
      console.log(`Selected ${selectedWordCount} words for Magic Edit from manual selection`);
      return { html: selectedHtml, text, from, to };
    },
    storeOriginalSelection: () => {
      if (editor && !editor.isDestroyed) {
        const { from, to } = editor.state.selection;
        let htmlSlice = '';
        if (from !== to) {
            const slice = editor.state.doc.slice(from, to);
            const tempEditor = new Editor({ content: slice.toJSON(), extensions: [StarterKit, MagicEditedMark] }); // Use toJSON() for slice
            htmlSlice = tempEditor.getHTML();
            tempEditor.destroy();
        }
        originalSelectionRef.current = { from, to, htmlSlice };
        console.log('[EditorPanel] Stored selection:', originalSelectionRef.current);
      } else {
        originalSelectionRef.current = null;
      }
    },
    updateScrapPaper: (htmlContent: string) => {
      setScrapPaperContent(htmlContent);
    },
    switchToScrapPaperTab: () => {
      setActiveUITab('scrap-paper');
    },
    replaceMainContentWithScrapPaper: () => {
      if (editor && !editor.isDestroyed && scrapPaperContent) {
        if (originalSelectionRef.current) {
          const { from, to } = originalSelectionRef.current;

          editor.chain().focus()
            .deleteRange({ from, to })
            .insertContentAt(from, scrapPaperContent, { updateSelection: true })
            .run();

          const insertedContentNode = scrapPaperEditor?.state.doc;
          const insertedContentSize = insertedContentNode?.content.size || 0;
          // Ensure end position does not exceed document size
          const docSize = editor.state.doc.content.size;
          const insertedContentEndPos = Math.min(from + insertedContentSize, docSize);


          if (from < insertedContentEndPos) { // Only set mark if range is valid
            editor.chain().focus()
              .setTextSelection({ from, to: insertedContentEndPos })
              .setMark(MagicEditedMark.name)
              .run();
            console.log(`[EditorPanel] Applied MagicEditedMark from ${from} to ${insertedContentEndPos}`);
          } else {
            console.warn(`[EditorPanel] Invalid range for applying mark: from ${from}, to ${insertedContentEndPos}. Content size: ${insertedContentSize}`);
          }

        } else {
          editor.commands.setContent(scrapPaperContent);
          const newSize = editor.state.doc.content.size;
          if (newSize > 0) {
            editor.chain().focus().setTextSelection({ from: 0, to: newSize }).setMark(MagicEditedMark.name).run();
            console.log(`[EditorPanel] Fallback: Applied MagicEditedMark to entire document (0 to ${newSize})`);
          }
          toast.warning("No specific text selection was active for replacement. Entire content updated and marked as edited.");
        }

        const newHtml = editor.getHTML();
        const newWordCount = countWordsInternal(editor.getText());
        onContentChange(newHtml, newWordCount);

        toast.success('Magic Edit™ applied to main draft.');
        setScrapPaperContent('');
        setActiveUITab('main-draft');
        originalSelectionRef.current = null;
      } else {
        toast.error('Error applying Magic Edit™. Scrap paper is empty or editor is not available.');
      }
    },
    triggerSave: () => {
        if(editor && !editor.isDestroyed) debouncedSaveHandler(editor);
    },

    // New functions for adjustable Magic Edit selection
    highlightMagicEditSelection: (from: number, to: number, wordCount: number) => {
      if (!editor || editor.isDestroyed) return;

      // Clear any existing preview marks first
      editor.chain().focus().unsetMark(MagicEditPreviewMark.name).run();

      // Apply preview mark to the specified range
      editor.chain()
        .focus()
        .setTextSelection({ from, to })
        .setMark(MagicEditPreviewMark.name, { 'data-word-count': wordCount.toString() })
        .run();

      console.log(`[EditorPanel] Highlighted Magic Edit selection from ${from} to ${to} (${wordCount} words)`);
    },

    clearMagicEditSelection: () => {
      if (!editor || editor.isDestroyed) return;

      // Remove all preview marks
      editor.chain().focus().unsetMark(MagicEditPreviewMark.name).run();
      console.log('[EditorPanel] Cleared Magic Edit selection preview');
    },

    getCurrentSelection: () => {
      if (!editor || editor.isDestroyed) return null;

      const { from, to, empty } = editor.state.selection;
      if (empty) return null;

      const text = editor.state.doc.textBetween(from, to);
      const slice = editor.state.selection.content();
      const tempEditor = new Editor({ content: slice.toJSON(), extensions: [StarterKit] });
      const html = tempEditor.getHTML();
      tempEditor.destroy();

      const wordCount = countWordsInternal(text);

      return { html, text, from, to, wordCount };
    },

    autoSelectForMagicEdit: () => {
      if (!editor || editor.isDestroyed) return null;

      // First check if user has made a selection
      const { from, to, empty } = editor.state.selection;
      if (!empty) {
        const text = editor.state.doc.textBetween(from, to);
        const wordCount = countWordsInternal(text);

        // Highlight the user's selection
        console.log(`[EditorPanel] Highlighting user selection from ${from} to ${to}, ${wordCount} words`);

        editor.chain().focus().unsetMark(MagicEditPreviewMark.name).run();
        const markApplied = editor.chain()
          .focus()
          .setTextSelection({ from, to })
          .setMark(MagicEditPreviewMark.name, { 'data-word-count': wordCount.toString() })
          .run();

        console.log(`[EditorPanel] User selection mark applied:`, markApplied);
        console.log(`[EditorPanel] Editor HTML after user mark:`, editor.getHTML().substring(0, 500));

        // Check if the mark was actually applied for user selection
        const htmlAfterUserMark = editor.getHTML();
        const hasUserPreviewMark = htmlAfterUserMark.includes('data-magic-edit-preview="true"');
        console.log(`[EditorPanel] User selection preview mark found in HTML:`, hasUserPreviewMark);

        const slice = editor.state.selection.content();
        const tempEditor = new Editor({ content: slice.toJSON(), extensions: [StarterKit] });
        const html = tempEditor.getHTML();
        tempEditor.destroy();

        console.log(`[EditorPanel] Auto-selected user's manual selection: ${wordCount} words`);
        return { html, text, from, to, wordCount };
      }

      // No user selection - find next unedited chunk
      // We need to call the getNextUneditedChunk logic directly here since we're in the same scope
      const MAX_WORDS = 5000;
      let collectedPlainText = "";
      let currentWordCount = 0;
      let chunkStartPos = -1;
      let chunkEndPos = -1;

      editor.state.doc.descendants((node, pos) => {
        if (currentWordCount >= MAX_WORDS) return false;

        if (node.isText) {
          const isNodeEdited = node.marks.some(mark => mark.type.name === MagicEditedMark.name);

          if (!isNodeEdited) {
            if (chunkStartPos === -1) {
              chunkStartPos = pos;
            }

            let textFromNode = node.text || "";
            const wordsInThisNode = countWordsInternal(textFromNode);

            if (currentWordCount + wordsInThisNode > MAX_WORDS) {
              const remainingWordsToTake = MAX_WORDS - currentWordCount;
              let wordsTakenFromThisNode = 0;
              let partialNodeText = "";
              const wordParts = textFromNode.split(/(\s+)/);

              for (const part of wordParts) {
                if (part.trim() !== "") {
                  if (wordsTakenFromThisNode < remainingWordsToTake) {
                    partialNodeText += part;
                    wordsTakenFromThisNode++;
                  } else {
                    break;
                  }
                } else {
                  partialNodeText += part;
                }
              }
              textFromNode = partialNodeText;
            }

            collectedPlainText += textFromNode;
            currentWordCount = countWordsInternal(collectedPlainText);
            chunkEndPos = pos + textFromNode.length;

            if (currentWordCount >= MAX_WORDS) {
              return false;
            }
          } else {
            if (chunkStartPos !== -1) {
              return false;
            }
          }
        }
        return true;
      });

      if (chunkStartPos === -1 || collectedPlainText.trim() === "") {
        console.log('[EditorPanel] No unedited content found for auto-select.');
        return null;
      }

      chunkEndPos = Math.max(chunkEndPos, chunkStartPos + collectedPlainText.trim().length);
      chunkEndPos = Math.min(chunkEndPos, editor.state.doc.content.size);

      // Get proper HTML for the chunk using the same method as getNextUneditedChunk
      let finalHtml = "";
      try {
        if (chunkStartPos !== -1 && chunkEndPos > chunkStartPos) {
          const finalSlice = editor.state.doc.slice(chunkStartPos, chunkEndPos);

          // Create temp editor with simplified extensions to avoid conflicts
          const tempEditorExtensions = [
            StarterKit.configure({
              history: false,
              heading: false,
              codeBlock: false,
            }),
            Placeholder.configure({
              placeholder: 'Temp editor content...',
              emptyEditorClass: 'is-editor-empty'
            }),
            CleanPasteExtension,
          ];

          const tempEditor = new Editor({
            content: finalSlice.content.toJSON(),
            extensions: tempEditorExtensions,
            editorProps: {
              attributes: { style: 'display: none;' }
            }
          });

          finalHtml = tempEditor.getHTML();
          tempEditor.destroy();

          console.log(`[EditorPanel] Successfully extracted HTML: ${finalHtml.substring(0, 100)}...`);
        }
      } catch (e) {
        console.warn('[EditorPanel] Error getting HTML for chunk:', e);
        // Fallback: wrap plain text in paragraph tags
        finalHtml = `<p>${collectedPlainText.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>')}</p>`;
      }

      const chunkData = {
        html: finalHtml || `<p>${collectedPlainText}</p>`,
        text: collectedPlainText,
        from: chunkStartPos,
        to: chunkEndPos,
        wordCount: currentWordCount
      };

      // Highlight the auto-selected chunk
      console.log(`[EditorPanel] About to highlight chunk from ${chunkData.from} to ${chunkData.to}`);

      // Clear any existing preview marks first
      editor.chain().focus().unsetMark(MagicEditPreviewMark.name).run();

      // Apply the preview mark to the selected range
      const markApplied = editor.chain()
        .focus()
        .setTextSelection({ from: chunkData.from, to: chunkData.to })
        .setMark(MagicEditPreviewMark.name, { 'data-word-count': chunkData.wordCount.toString() })
        .run();

      console.log(`[EditorPanel] Mark applied result:`, markApplied);
      console.log(`[EditorPanel] Current selection after mark:`, editor.state.selection);
      console.log(`[EditorPanel] Editor HTML after mark:`, editor.getHTML().substring(0, 500));

      // Check if the mark was actually applied by looking for the data attribute in the HTML
      const htmlAfterMark = editor.getHTML();
      const hasPreviewMark = htmlAfterMark.includes('data-magic-edit-preview="true"');
      console.log(`[EditorPanel] Preview mark found in HTML:`, hasPreviewMark);

      if (!hasPreviewMark) {
        console.warn(`[EditorPanel] Preview mark was not applied to the HTML. Trying alternative approach...`);
        // Try applying the mark again with a different approach
        editor.commands.setTextSelection({ from: chunkData.from, to: chunkData.to });
        editor.commands.setMark(MagicEditPreviewMark.name, { 'data-word-count': chunkData.wordCount.toString() });
      }

      console.log(`[EditorPanel] Auto-selected unedited chunk: ${chunkData.wordCount} words`);
      return chunkData;
    }
  }));

  const handleDiscardRewrite = () => {
    setScrapPaperContent('');
    setActiveUITab('main-draft');
    originalSelectionRef.current = null;
    toast.info('Magic Edit™ suggestion discarded.');
  };

  if (!editor) {
    return <div className="flex justify-center items-center min-h-[300px]"><Loader2 className="animate-spin h-8 w-8 text-primary" /> <span className="ml-2">Loading Editor...</span></div>;
  }

  return (
    <div className={cn("editor-panel text-neutral-100 mx-auto my-6 p-0 relative max-w-5xl w-full min-h-[80vh] flex flex-col items-center justify-center")}>
      <div className="fixed bottom-20 right-6 z-50 flex items-center justify-center space-x-2">
        {saveStatus === 'saving' && (
          <div className="flex items-center bg-black/80 backdrop-blur-lg px-4 py-2 rounded-full shadow-xl border border-gray-800/50 text-sm transition-all duration-300 animate-in slide-in-from-bottom-5">
            <Loader2 className="h-3.5 w-3.5 animate-spin mr-2" />
            <span className="text-gray-300">Saving changes</span>
          </div>
        )}
        {saveStatus === 'saved' && (
          <div className="flex items-center bg-black/80 backdrop-blur-lg px-4 py-2 rounded-full shadow-xl border border-green-900/40 text-sm transition-all duration-300 animate-in fade-in">
            <CheckCircle className="h-3.5 w-3.5 mr-2 text-green-400" />
            <span className="text-green-300">Changes saved</span>
          </div>
        )}
      </div>

      <div className="mb-8 md:mb-10 text-center relative">
        <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-amber-600/0 via-amber-500/50 to-amber-600/0"></div>
        <h1 className="font-serif text-3xl sm:text-4xl font-bold break-words text-foreground tracking-tight">
          {chapter.title.startsWith(`Chapter ${chapter.chapter_order}`) ? chapter.title : `Chapter ${chapter.chapter_order}: ${chapter.title}`}
        </h1>
        {onSubtitleChange && (
          <div className="mt-4">
            <input
              type="text"
              placeholder="Chapter Subtitle (Optional)"
              value={subtitle || ""}
              onChange={handleSubtitleInputChange}
              className="w-full max-w-2xl mx-auto text-center bg-transparent border-b border-border/30 focus:border-amber-500/60 focus:ring-0 focus:outline-none py-2 px-2 text-muted-foreground focus:text-foreground text-lg italic font-serif transition-all duration-300"
            />
          </div>
        )}
        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-amber-600/0 via-amber-500/50 to-amber-600/0"></div>
      </div>

      <Tabs value={activeUITab} onValueChange={(value) => setActiveUITab(value as 'main-draft' | 'scrap-paper')} className="w-full">
        {(activeUITab === 'scrap-paper' || scrapPaperContent.trim()) && (
          <div className="flex flex-wrap justify-center items-center gap-3 mb-6">
            <div className="flex items-center bg-black/60 backdrop-blur-md rounded-full border border-gray-800/30 p-1 shadow-lg">
              <TabsList className="bg-transparent rounded-full overflow-hidden border-none">
                <TabsTrigger
                  value="main-draft"
                  title="Your primary writing area"
                  className="px-5 py-2 text-sm font-medium rounded-full data-[state=active]:shadow-md transition-all duration-300 text-gray-300 data-[state=active]:text-white"
                >
                  Main Draft
                </TabsTrigger>
                <TabsTrigger
                  value="scrap-paper"
                  disabled={!scrapPaperContent.trim()}
                  className={cn("px-5 py-2 text-sm font-medium rounded-full transition-all duration-300",
                    scrapPaperContent.trim() ? "text-amber-400 data-[state=active]:bg-amber-950/30" : "")}
                  title="View generated text"
                >
                  Edited Text
                  {scrapPaperContent.trim() && (
                    <span className="ml-2 px-2 py-0.5 text-[10px] bg-amber-500/30 text-amber-300 rounded-full font-bold animate-pulse">
                      NEW
                    </span>
                  )}
                </TabsTrigger>
              </TabsList>
            </div>

            {activeUITab === 'scrap-paper' && scrapPaperContent.trim() && (
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  className="border-red-500/30 hover:bg-red-500/10 text-red-400 h-9 px-4 rounded-full text-sm font-medium"
                  onClick={handleDiscardRewrite}
                >
                  <X className="h-4 w-4 mr-2" />Discard
                </Button>
                <Button
                  size="sm"
                  className="bg-gradient-to-r from-green-600 to-teal-500 hover:from-green-500 hover:to-teal-400 text-black h-9 px-4 rounded-full text-sm font-semibold shadow-lg"
                  onClick={() => {
                    if (typeof ref !== 'function' && ref && ref.current) {
                      ref.current.replaceMainContentWithScrapPaper();
                    }
                  }}
                >
                  <Check className="h-4 w-4 mr-2" />Apply
                </Button>
              </div>
            )}
          </div>
        )}

        <TabsContent value="main-draft" className="mt-0 editor-content-wrapper">
          <div
            className="relative z-0 mb-8"
            style={{
              backgroundImage: 'radial-gradient(circle at center top, rgba(255,215,0,0.03) 0%, rgba(0,0,0,0) 70%)',
            }}
          >
            <div
              ref={editorContainerRef}
              className="editor-scroll-container overflow-y-auto relative flex justify-center rounded-xl"
              style={{
                maxHeight: 'calc(100vh - 8rem)',
                minHeight: '75vh',
              }}
            >
              <div className="editor-container relative">
                <div
                  className="absolute inset-0 opacity-5 z-[-1] pointer-events-none"
                  style={{
                    backgroundSize: "20px",
                    backgroundImage: "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoBAMAAAB+0KVeAAAAHlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC3KG9qAAAACXRSTlMA9hTpIcw1FpvvP7dZAAAAW0lEQVQoz2MYtkAUiDnhmqQYhBgZhYBEIEwwMQgpMTAIglhiQKIMDIIgwsaFYYCJEUzYMDJCGMIMjEDCwlDCgIABYaAyC0MYAhjwmOPAwEAbhgvNQJsHKx0GAACDxSTM2m6djAAAAABJRU5ErkJggg==)",
                  }}
                ></div>

                {!zenMode && (
                  <div className={`floating-metrics${showFloatingMetrics ? ' show' : ''}`}>
                    <div>
                      Words: {countWordsInternal(editor?.getText() || '')}
                    </div>
                    <div>
                      {(() => {
                        const wordCount = countWordsInternal(editor?.getText() || '');
                        const goal = (chapter as any).word_goal || 50000;
                        const progress = goal > 0 ? Math.min(100, Math.round((wordCount / goal) * 100)) : 0;
                        return `Progress: ${progress}%`;
                      })()}
                    </div>
                  </div>
                )}
                {zenMode && zenHover && (
                  <div
                    style={{
                      position: 'fixed',
                      top: 0,
                      left: 0,
                      width: '100vw',
                      height: 40,
                      background: 'rgba(20,20,20,0.92)',
                      color: '#FFD700',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      zIndex: 2000,
                      cursor: 'pointer',
                      fontSize: '1.1rem',
                      letterSpacing: '0.04em',
                      fontFamily: "'EB Garamond', Georgia, serif",
                      fontWeight: 500,
                      boxShadow: '0 2px 12px #0008'
                    }}
                    onClick={() => setZenMode(false)}
                  >
                    Zen Mode — <span style={{ marginLeft: 8, textDecoration: 'underline' }}>Exit</span> (Esc)
                  </div>
                )}

                <EditorContent
                  editor={editor}
                  className="font-serif relative z-10"
                />
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="scrap-paper" className="mt-0 editor-content-wrapper">
          {scrapPaperContent.trim() ? (
            <div className="relative">
              <div className="mb-4 p-5 backdrop-blur-md bg-gradient-to-b from-gray-900/60 to-black/60 rounded-xl border border-gray-700/30 text-sm shadow-xl">
                <p className="font-semibold text-white flex items-center">
                  <Sparkles className="h-4 w-4 mr-2 text-amber-400" style={{filter: 'drop-shadow(0 0 2px rgba(255,215,0,0.5))'}} />
                  Magic Edit™ Result
                </p>
                <p className="text-gray-300 mt-2 text-sm">
                  Your text has been professionally edited while preserving your voice and style.
                  Review the changes and click <span className="text-green-400 font-medium">Apply</span> to use this version.
                </p>
              </div>

              <div
                className="editor-scroll-container overflow-y-auto bg-white/5 border border-gray-800/40 rounded-xl shadow-xl"
                style={{
                  maxHeight: 'calc(100vh - 24rem)',
                  minHeight: '300px',
                }}
              >
                <EditorContent
                  editor={scrapPaperEditor}
                  className="p-6 md:p-8 font-serif"
                />
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center min-h-[300px] p-12 text-center bg-gradient-to-b from-gray-900/20 to-black/40 rounded-xl border border-gray-800/30">
              <div className="bg-black/40 p-6 rounded-full mb-5 backdrop-blur-md">
                <RotateCcw className="h-12 w-12 text-gray-700" />
              </div>
              <h3 className="text-xl font-medium mb-2 text-gray-400">No Edited Text Available</h3>
              <p className="text-gray-500 max-w-sm">
                Use the Magic Edit™ feature from the toolbar below to generate a professionally polished version of your text.
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* The MagicEditDialog instance previously rendered here has been removed.
          It is now solely managed by the parent component (WritingDeskPage). */}
    </div>
  );
});

EditorPanel.displayName = 'EditorPanel';
