'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Loader2, <PERSON><PERSON>ir<PERSON>, Check, X, Sparkles } from 'lucide-react';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { MagicEditCreditsDisplay } from '@/components/magic-edit-credits-display';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useCredits } from '@/lib/hooks/use-credits';

interface MagicEditDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  initialTextHtml: string;
  isGenerating: boolean;
  generatedSuggestion: string | null;
  onGenerateMagicEdit: (html: string) => Promise<void>;
  onAcceptToScrapPaper: (editedHtml: string) => void;
  chapters?: { id: string; title: string; content: string }[];
  selectedChapterId?: string;
  onSelectChapter?: (chapterId: string) => void;
  onAcceptAndEdit?: (chapterId: string) => void;
}

export function MagicEditDialog({
  isOpen,
  onOpenChange,
  initialTextHtml,
  isGenerating,
  generatedSuggestion,
  onGenerateMagicEdit,
  onAcceptToScrapPaper,
  chapters = [],
  selectedChapterId,
  onSelectChapter,
  onAcceptAndEdit,
}: MagicEditDialogProps) {
  // Use the credits hook for consistent credit display
  const { magicEditsRemaining, isLoading: creditsLoading, refreshCredits } = useCredits();
  const [wordCount, setWordCount] = useState<number>(0);
  const [isTextTooLong, setIsTextTooLong] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<'input' | 'result'>('input');
  // Chapter selection logic might be secondary if we are focusing on initialTextHtml
  const [chapterIdForFullEdit, setChapterIdForFullEdit] = useState<string>(selectedChapterId || (chapters[0]?.id ?? ''));
  const [chunkIndex, setChunkIndex] = useState(0); // For full chapter chunking

  // Word count for the initialTextHtml (the selected chunk)
  const countWordsInHtml = (html: string): number => {
    if (!html) return 0;
    // Create a temporary div to parse HTML and get text
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    const text = tempDiv.textContent || tempDiv.innerText || '';
    return text.trim().split(/\s+/).filter(Boolean).length;
  };

  useEffect(() => {
    console.log('[MagicEditDialog] Received initialTextHtml:', initialTextHtml ? initialTextHtml.substring(0, 200) + '...' : 'null/empty');
    if (initialTextHtml) {
      const count = countWordsInHtml(initialTextHtml);
      console.log('[MagicEditDialog] Calculated word count:', count, 'from HTML length:', initialTextHtml.length);
      setWordCount(count);
      setIsTextTooLong(count > 50000); // Max 50000 words for a single API call
      setActiveTab('input'); // Reset to input tab when new initialText comes
    } else {
      console.log('[MagicEditDialog] No initialTextHtml provided, setting word count to 0');
      setWordCount(0);
    }
  }, [initialTextHtml]);

  useEffect(() => {
    if (selectedChapterId) setChapterIdForFullEdit(selectedChapterId);
    else if (chapters.length > 0) setChapterIdForFullEdit(chapters[0].id);
  }, [selectedChapterId, chapters]);

  useEffect(() => {
    if (generatedSuggestion) {
      setActiveTab('result');
    } else {
      // If dialog is opened with initialTextHtml, stay on input tab
      // or if suggestion is cleared, go back to input.
      setActiveTab('input');
    }
  }, [generatedSuggestion]);

  const handleGenerateForSelectedText = async () => {
    if (isTextTooLong) {
      toast.error("Selected text is too long (max 50,000 words). Please select a smaller chunk.");
      return;
    }
    if (!initialTextHtml.trim()) {
      toast.error("No text selected or selected text is empty.");
      return;
    }

    const creditsRequired = Math.ceil(wordCount / 5000);

    // Force refresh credits before validation to ensure accuracy
    await refreshCredits();

    // Get the most up-to-date credit count after refresh
    const currentCredits = magicEditsRemaining;

    if (creditsRequired > currentCredits) {
      toast.error(`This edit requires ${creditsRequired} credits but you only have ${currentCredits} remaining. Please upgrade to get more credits or edit a smaller section.`);
      return;
    }

    try {
      await onGenerateMagicEdit(initialTextHtml); // This calls handleGenerateMagicEditApiCall in page.tsx
    } catch (error: any) {
      // Check if it's a credit-related error and refresh credits
      if (error.message.includes('credits') || error.message.includes('remaining')) {
        await refreshCredits();
      }
      toast.error(`Failed to generate Magic Edit: ${error.message || 'Unknown error'}`);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] md:max-w-[900px] lg:max-w-[1000px] xl:max-w-[1200px] max-h-[90vh] flex flex-col p-0">
        <DialogHeader className="flex flex-row items-center justify-between p-6 border-b">
          <div className="flex items-center">
            <Sparkles className="h-5 w-5 mr-2 text-amber-500" style={{filter: 'drop-shadow(0 0 3px rgba(255,215,0,0.3))'}} />
            <DialogTitle>Magic Edit</DialogTitle>
          </div>
          <div className="flex items-center gap-2">
            {creditsLoading ? (
              <span className="text-sm text-muted-foreground">Loading credits...</span>
            ) : magicEditsRemaining > 0 ? (
              <span className="text-sm text-amber-500">
                {magicEditsRemaining} credits remaining
              </span>
            ) : (
              <div className="flex items-center gap-2">
                <span className="text-sm text-red-400">0 credits remaining</span>
                <button
                  onClick={() => window.open('/pricing', '_blank')}
                  className="px-3 py-1 bg-amber-500 hover:bg-amber-600 text-black rounded-md text-sm font-medium transition-colors"
                >
                  Buy More
                </button>
              </div>
            )}
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'input' | 'result')} className="flex-1 flex flex-col min-h-0">
          <TabsList className="grid w-full grid-cols-2 rounded-none border-b">
            <TabsTrigger value="input">Original Text (Selected Chunk)</TabsTrigger>
            <TabsTrigger value="result" disabled={!generatedSuggestion && !isGenerating}>Edited Suggestion</TabsTrigger>
          </TabsList>

          <TabsContent value="input" className="flex-1 overflow-y-auto p-6 m-0">
            <div className="prose prose-sm sm:prose-base dark:prose-invert max-w-none rounded-md border p-4 min-h-[200px] bg-background"
                 dangerouslySetInnerHTML={{ __html: initialTextHtml || "<p>No text selected for Magic Edit.</p>" }} />
            <div className="mt-2 text-xs text-muted-foreground">
              Word Count: {wordCount}
              {isTextTooLong ? (
                <span className="text-red-500"> (Exceeds 50,000 word limit)</span>
              ) : wordCount > 0 ? (
                <span className="text-amber-600"> • Costs {Math.ceil(wordCount / 5000)} credit{Math.ceil(wordCount / 5000) !== 1 ? 's' : ''}</span>
              ) : null}
            </div>
          </TabsContent>

          <TabsContent value="result" className="flex-1 overflow-y-auto p-6 m-0">
            {isGenerating && (
              <div className="flex items-center justify-center min-h-[200px]">
                <Loader2 className="h-8 w-8 animate-spin text-amber-500" />
                <p className="ml-2">Generating your Magic Edit...</p>
              </div>
            )}
            {!isGenerating && generatedSuggestion && (
              <div className="prose prose-sm sm:prose-base dark:prose-invert max-w-none rounded-md border p-4 min-h-[200px] bg-background"
                   dangerouslySetInnerHTML={{ __html: generatedSuggestion }} />
            )}
            {!isGenerating && !generatedSuggestion && (
              <div className="flex items-center justify-center min-h-[200px]">
                <p>No suggestion generated yet.</p>
              </div>
            )}
          </TabsContent>
        </Tabs>

        <DialogFooter className="p-6 border-t flex flex-col sm:flex-row sm:justify-between items-center">
          <div className="text-xs text-muted-foreground mb-4 sm:mb-0">
            Magic Edit refines your text. Review suggestions carefully.
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            {activeTab === 'input' && (
              <Button
                className="bg-gradient-to-r from-amber-500 via-orange-500 to-amber-600 hover:from-amber-400 hover:to-amber-500 text-white shadow-md"
                onClick={handleGenerateForSelectedText}
                disabled={isGenerating || isTextTooLong || !initialTextHtml.trim() || magicEditsRemaining < Math.ceil(wordCount / 5000)}
              >
                {isGenerating ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : magicEditsRemaining < Math.ceil(wordCount / 5000) ? (
                  `Need ${Math.ceil(wordCount / 5000)} Credits (${magicEditsRemaining} Available)`
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Generate Edit ({Math.ceil(wordCount / 5000)} credit{Math.ceil(wordCount / 5000) !== 1 ? 's' : ''})
                  </>
                )}
              </Button>
            )}
            {activeTab === 'result' && generatedSuggestion && (
              <Button
                className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-400 hover:to-emerald-500 text-white shadow-md"
                onClick={() => onAcceptToScrapPaper(generatedSuggestion)}
                disabled={isGenerating}
              >
                <Check className="h-4 w-4 mr-2" />
                Accept to Scrap Paper
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
