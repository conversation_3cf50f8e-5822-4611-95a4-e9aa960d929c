.floating-metrics {
  position: absolute;
  bottom: 2rem;
  right: 2rem;
  background: rgba(20, 20, 20, 0.85);
  color: #FFD700;
  font-size: 0.85rem;
  padding: 0.6rem 1rem;
  border-radius: 12px;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  pointer-events: none;
  z-index: 1000;
}
.floating-metrics.show {
  opacity: 1;
  pointer-events: auto;
}

/* Centered, spacious editor layout */
.editor-scroll-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-left: 2.5rem;
  padding-right: 2.5rem;
  min-height: 75vh;
}

.editor-container {
  max-width: 900px;
  width: 100%;
  margin: 0 auto;
  background: transparent;
  position: relative;
  padding: 2.5rem 0;
  border-radius: 18px;
  box-shadow: none;
}

/* Typography overhaul */
.editor-styling-area {
  font-family: 'E<PERSON> Garamond', Georgia, serif;
  font-size: 19px;
  line-height: 1.7;
  color: #f7f7f7;
  background: transparent;
}

.editor-styling-area p {
  margin-bottom: 1.2em;
}

.editor-styling-area h1,
.editor-styling-area h2 {
  font-family: 'E<PERSON> Garamond', Georgia, serif;
  font-weight: 700;
  letter-spacing: 0.01em;
  margin-top: 2.2em;
  margin-bottom: 1.2em;
  line-height: 1.2;
  color: #FFD700;
  text-shadow: 0 1px 8px #C2880033;
}

.editor-styling-area h1 {
  font-size: 2.6rem;
  letter-spacing: 0.03em;
  text-align: center;
}

.editor-styling-area h2 {
  font-size: 2rem;
  text-align: center;
}

.editor-styling-area strong {
  color: #FFD700;
  font-weight: 600;
}

.editor-styling-area em {
  color: #C28800;
}

body.dark,
.editor-panel {
  background: #0b0b0b !important;
}

.editor-panel {
  border-radius: 0;
  box-shadow: none;
}

/* Magic Edit highlighting styles */
span[data-magic-edited="true"] {
  background-color: rgba(255, 215, 0, 0.15);
  border-left: 3px solid #FFD700;
  padding-left: 4px;
  margin-left: -4px;
  border-radius: 2px;
  position: relative;
  transition: background-color 0.2s ease;
}

span[data-magic-edited="true"]:hover {
  background-color: rgba(255, 215, 0, 0.25);
}

/* Optional: Add a subtle animation on first application */
span[data-magic-edited="true"].newly-edited {
  animation: magicEditGlow 1.5s ease-out forwards;
}

@keyframes magicEditGlow {
  0% {
    background-color: rgba(255, 215, 0, 0.3);
    box-shadow: 0 0 8px rgba(255, 215, 0, 0.4);
  }
  100% {
    background-color: rgba(255, 215, 0, 0.15);
    box-shadow: none;
  }
}

/* Magic Edit preview selection */
.magic-edit-preview-selection,
span[data-magic-edit-preview="true"] {
  background-color: rgba(255, 165, 0, 0.2) !important;
  border: 2px dashed #FFA500 !important;
  border-radius: 4px !important;
  padding: 2px !important;
  position: relative !important;
  display: inline-block !important;
  min-height: 1em !important;
}

.magic-edit-preview-selection::before,
span[data-magic-edit-preview="true"]::before {
  content: "Magic Edit Selection (" attr(data-word-count) " words)";
  position: absolute;
  top: -20px;
  left: 0;
  font-size: 10px;
  color: #FFA500;
  background: rgba(0, 0, 0, 0.8);
  padding: 2px 6px;
  border-radius: 3px;
  white-space: nowrap;
  z-index: 10;
  pointer-events: none;
}
