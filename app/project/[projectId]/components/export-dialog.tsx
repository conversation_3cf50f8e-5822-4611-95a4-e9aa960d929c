'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import { BookText, Download, FileDown, Lock } from 'lucide-react';

import { supabase } from '@/lib/supabase/client';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';

interface ExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: string;
  projectTitle: string;
}

type ExportFormat = 'pdf' | 'docx' | 'epub';

export function ExportDialog({
  open,
  onOpenChange,
  projectId,
  projectTitle,
}: ExportDialogProps) {
  const [exportFormat, setExportFormat] = useState<ExportFormat>('pdf');
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      // Fetch all chapters for the project
      const { data: chapters, error } = await supabase
        .from('chapters')
        .select('title, content, order')
        .eq('project_id', projectId)
        .order('order', { ascending: true });
      
      if (error) throw error;
      
      if (!chapters || chapters.length === 0) {
        toast.error('No content to export');
        return;
      }
      
      // For now, we'll just simulate the export
      // In a real implementation, you would process the chapters and use the
      // appropriate library to generate the export file
      
      setTimeout(() => {
        toast.success(`${projectTitle} exported as ${exportFormat.toUpperCase()}`);
        onOpenChange(false);
      }, 1500);
      
      // Here you would actually implement:
      // - For PDF: jsPDF + html2canvas
      // - For DOCX: html-docx-js + file-saver
      // - For EPUB: epub-gen or similar (may require server-side)
      
    } catch (error) {
      console.error('Error exporting document:', error);
      toast.error('Failed to export document');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Export Your Book</DialogTitle>
          <DialogDescription>
            Choose a format to export your book for printing or sharing
          </DialogDescription>
        </DialogHeader>

        <div className="py-6">
          <RadioGroup
            value={exportFormat}
            onValueChange={(value) => setExportFormat(value as ExportFormat)}
            className="space-y-4"
          >
            <div className="flex items-center space-x-2 border rounded-md p-3 hover:bg-muted/50 transition">
              <RadioGroupItem value="pdf" id="pdf" />
              <Label htmlFor="pdf" className="flex-1 cursor-pointer font-normal">
                <div className="flex items-center">
                  <FileDown className="h-5 w-5 mr-2 text-blue-500" />
                  <div>
                    <p className="font-medium">PDF</p>
                    <p className="text-sm text-muted-foreground">Portable Document Format</p>
                  </div>
                </div>
              </Label>
            </div>
            
            <div className="flex items-center space-x-2 border rounded-md p-3 hover:bg-muted/50 transition">
              <RadioGroupItem value="docx" id="docx" />
              <Label htmlFor="docx" className="flex-1 cursor-pointer font-normal">
                <div className="flex items-center">
                  <BookText className="h-5 w-5 mr-2 text-green-500" />
                  <div>
                    <p className="font-medium">DOCX</p>
                    <p className="text-sm text-muted-foreground">Microsoft Word</p>
                  </div>
                </div>
              </Label>
            </div>
            
            <div className="flex items-center space-x-2 border rounded-md p-3 hover:bg-muted/50 transition">
              <RadioGroupItem value="epub" id="epub" />
              <Label htmlFor="epub" className="flex-1 cursor-pointer font-normal">
                <div className="flex items-center">
                  <BookText className="h-5 w-5 mr-2 text-orange-500" />
                  <div>
                    <p className="font-medium">EPUB</p>
                    <p className="text-sm text-muted-foreground">Electronic Publication</p>
                  </div>
                </div>
              </Label>
            </div>
          </RadioGroup>

          <p className="text-xs text-muted-foreground mt-4">
            All export formats are available free. Need AI writing assistance? <span className="font-medium text-primary">Purchase Magic Edit credits</span> on the pricing page.
          </p>
        </div>

        <DialogFooter>
          <Button
            variant="ghost"
            onClick={() => onOpenChange(false)}
            disabled={isExporting}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleExport}
            disabled={isExporting}
          >
            {isExporting ? (
              <>
                <div className="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export as {exportFormat.toUpperCase()}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}