'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { <PERSON>, Sparkles, ExternalLink, LucideLoader2 } from 'lucide-react';
import { useAuth } from '@/lib/providers/auth-provider';
import { useBookMemoryAvailability, useUserPlan } from '@/hooks/use-user-plan';

import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';

interface BookMemory {
  id: string;
  project_id: string;
  chapter_id: string;
  summary_text: string;
  created_at: string;
  chapters: {
    title: string;
    chapter_order: number;
  };
}

interface BookMemoryDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: string;
}

export function BookMemoryDrawer({
  open,
  onOpenChange,
  projectId,
}: BookMemoryDrawerProps) {
  const [memories, setMemories] = useState<BookMemory[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const { user, session, getAuthToken } = useAuth();
  const userPlanDetails = useUserPlan(user?.id);
  const { isPaidUser, isBookMemoryEnabled } = useBookMemoryAvailability(user?.id, userPlanDetails);
  
  // Fetch BookMemory™ data when drawer opens
  useEffect(() => {
    if (open && isPaidUser) {
      fetchBookMemories();
    }
  }, [open, projectId, isPaidUser, getAuthToken]);
  
  // Fetch BookMemory™ entries for this project
  const fetchBookMemories = async () => {
    try {
      setIsLoading(true);
      
      // Get auth token using the helper method
      let authToken = getAuthToken();
      
      // Prepare headers with authentication
      const headers: HeadersInit = { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      };
      
      if (authToken) {
        // Primary auth method: Authorization header with bearer token
        headers['Authorization'] = `Bearer ${authToken}`;
        // Backup method: Custom header with JWT
        headers['x-supabase-jwt'] = authToken;
        console.log('[BookMemoryDrawer] Using token for authentication');
      }
      
      const response = await fetch(`/api/book-memory?projectId=${projectId}`, {
        headers,
        credentials: 'include' // Include cookies for session-based fallback
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch BookMemory™ data');
      }
      
      const data = await response.json();
      
      if (data.memories) {
        // Sort by chapter order
        const sortedMemories = data.memories.sort((a: BookMemory, b: BookMemory) => 
          (a.chapters?.chapter_order || 0) - (b.chapters?.chapter_order || 0)
        );
        
        setMemories(sortedMemories);
      }
    } catch (error: any) {
      console.error('Error fetching BookMemory™ data:', error);
      toast.error('Failed to load BookMemory™ data');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="w-full sm:max-w-xl">
        <SheetHeader className="mb-6">
          <SheetTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-amber-500" />
            <span className="bg-gradient-to-r from-amber-500 via-amber-600 to-amber-500 bg-clip-text text-transparent">
              BookMemory™
            </span>
          </SheetTitle>
          <SheetDescription>
            AI-generated chapter summaries that help maintain consistency across your book.
          </SheetDescription>
        </SheetHeader>
        
        {isLoading ? (
          // Loading state
          <div className="flex flex-col items-center justify-center h-[50vh]">
            <LucideLoader2 className="h-8 w-8 animate-spin text-muted-foreground mb-4" />
            <p className="text-muted-foreground">Loading BookMemory™ data...</p>
          </div>
        ) : memories.length === 0 ? (
          // Empty state
          <div className="text-center p-6 border border-dashed rounded-lg">
            <Brain className="mx-auto h-12 w-12 text-muted-foreground/50 mb-4" />
            <h3 className="text-lg font-medium mb-2">No BookMemory™ Yet</h3>
            <p className="text-muted-foreground mb-6">
              BookMemory™ is generated automatically when you save chapters. As you work on your book, summaries will appear here.
            </p>
          </div>
        ) : (
          // BookMemory™ list
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground mb-4">
              These AI-generated summaries help maintain continuity in your REWRITE™ operations. They are automatically generated when you save chapters.
            </p>
            
            <Accordion type="single" collapsible className="w-full">
              {memories.map((memory) => (
                <AccordionItem key={memory.id} value={memory.id}>
                  <AccordionTrigger className="text-sm hover:no-underline">
                    <span className="flex items-start">
                      <span className="font-medium">
                        {memory.chapters?.title || `Chapter ${memory.chapters?.chapter_order || '?'}`}
                      </span>
                      <span className="text-xs text-muted-foreground ml-2 mt-0.5">
                        Last updated: {new Date(memory.created_at).toLocaleDateString()}
                      </span>
                    </span>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="p-3 bg-muted/50 rounded-md text-sm">
                      {memory.summary_text}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
            
            <p className="text-xs text-muted-foreground mt-4">
              BookMemory™ summaries are automatically provided to the AI when you use REWRITE™ to maintain consistency.
            </p>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
}