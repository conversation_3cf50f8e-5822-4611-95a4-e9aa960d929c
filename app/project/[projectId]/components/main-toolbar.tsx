'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { 
  Save, 
  PanelLeftOpen, 
  Download,
  Maximize, 
  Minimize,
  LogOut,
  Wand2, // Template icon
  Volume2,
  VolumeX,
  Clock,
  Share2,
  ChevronUp, // For show toolbar
  ChevronDown, // For hide toolbar
  ArrowRight, // For Save & New Chapter
  Menu, // Mobile menu
  Loader2,
  Settings,
  Sparkles, // Magic Edit icon
  Brain,    // BookMemory icon
  X         // Zen Mode exit icon
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { cn } from '@/lib/utils';
import { useAuth } from '@/lib/providers/auth-provider';
import { TemplateSelectorDialog } from './template-selector-dialog';
import { ExportDialog } from './export-dialog';
import { ProjectSettingsDialog } from './project-settings-dialog';
import { BookMemoryDrawer } from './book-memory-drawer';
import { MagicEditCreditsDisplay } from '@/components/magic-edit-credits-display';

interface ProjectForToolbar {
  id: string;
  title: string;
  author?: string | undefined; 
  cover_image_url?: string | null;
  word_goal?: number;
}

interface MainToolbarProps {
  onChaptersClick: () => void;
  projectId: string;
  projectTitle: string;
  onNewChapter?: () => Promise<void>; 
  project: ProjectForToolbar; 
  onProjectUpdate: (updatedProject: Partial<ProjectForToolbar>) => void; 
  
  onTriggerMagicEdit: () => void;
  isRewritePossible?: boolean; 
  isGeneratingAiContent?: boolean; 
  zenMode: boolean;
  setZenMode: React.Dispatch<React.SetStateAction<boolean>>;
}

export function MainToolbar({ 
  onChaptersClick, 
  projectId, 
  projectTitle, 
  onNewChapter,
  project,
  onProjectUpdate,
  onTriggerMagicEdit, 
  isRewritePossible = true, 
  isGeneratingAiContent = false,
  zenMode,
  setZenMode
}: MainToolbarProps) {
  const [isFullView, setIsFullView] = useState(false);
  const [isSoundOn, setIsSoundOn] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [showTemplateDialog, setShowTemplateDialog] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showSettingsDialog, setShowSettingsDialog] = useState(false);
  const [showBookMemoryDrawer, setShowBookMemoryDrawer] = useState(false);
  const [isToolbarVisible, setIsToolbarVisible] = useState(true);
  const [isCreatingChapter, setIsCreatingChapter] = useState(false);
  const router = useRouter();
  const { user, signOut } = useAuth();
  
  const handleSave = () => {
    toast.success('Changes saved (simulated)');
  };

  const toggleFullView = () => setIsFullView(!isFullView);
  const toggleToolbarVisibility = () => setIsToolbarVisible(!isToolbarVisible);
  const toggleSound = () => {
    setIsSoundOn(!isSoundOn);
    toast.info(isSoundOn ? 'Ambient sound OFF' : 'Ambient sound ON (feature placeholder)');
  };
  const [showSoundOptions, setShowSoundOptions] = useState(false);

  const handleLogout = async () => {
    await signOut();
    router.push('/login'); 
  };

  const handleShare = () => toast.info('Share link copied (feature placeholder)');
  const handleTimeline = () => toast.info('Timeline feature coming soon');

  const ToolbarButton = ({ 
    icon, 
    onClick, 
    tooltip,
    className,
    disabled = false
  }: { 
    icon: React.ReactNode; 
    onClick?: () => void; 
    tooltip: string;
    className?: string;
    disabled?: boolean;
  }) => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={onClick}
            className={cn("h-8 w-8 p-1.5", className)} // Adjusted padding for icons
            disabled={disabled}
          >
            {icon}
          </Button>
        </TooltipTrigger>
        <TooltipContent side="top"> {/* Changed side to top for better visibility */}
          <p>{tooltip}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );

  return (
    <>
      {/* Persistent Chapters & Settings Buttons */}
      <div className="fixed left-4 top-24 z-40 hidden md:block space-y-4"> {/* Adjusted z-index */}
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="icon" onClick={onChaptersClick} className="h-10 w-10 rounded-full shadow-md bg-background border-border hover:bg-muted transition-colors">
                <PanelLeftOpen className="h-5 w-5 text-primary" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right"><p>Chapters</p></TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="icon" onClick={() => setShowSettingsDialog(true)} className="h-10 w-10 rounded-full shadow-md bg-background border-border hover:bg-muted transition-colors">
                <Settings className="h-5 w-5 text-primary" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right"><p>Project Settings</p></TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      
      {/* Removed persistent Magic Edit Credits from desktop */}
      
      {!isToolbarVisible && (
        <div className="fixed bottom-2 left-1/2 transform -translate-x-1/2 z-50 hidden md:block">
          <Button variant="outline" size="sm" onClick={toggleToolbarVisibility} className="h-7 rounded-full px-3 shadow-lg flex items-center gap-1.5 bg-background/80 backdrop-blur-sm border-muted-foreground/20 hover:border-primary">
            <ChevronUp className="h-4 w-4 text-primary" />
            <span className="text-xs font-medium">Show Toolbar</span>
          </Button>
        </div>
      )}
      
      <div className={cn("fixed bottom-6 right-6 z-50 hidden md:flex transition-all duration-300 ease-out", !isToolbarVisible && "opacity-0 pointer-events-none translate-y-full")}>
        <div className="bg-black/90 border border-border rounded-full p-2 flex gap-2 shadow-2xl">
          {/* Zen Mode toggle only */}
          <ToolbarButton
            icon={
              zenMode
                ? <X className="h-5 w-5 text-amber-400" />
                : <Wand2 className="h-5 w-5 text-amber-400" />
            }
            onClick={() => setZenMode(!zenMode)}
            tooltip={zenMode ? "Exit Zen Mode" : "Zen Mode"}
            className={zenMode ? "bg-amber-900/30" : ""}
          />
        </div>
      </div>

      <div className="fixed md:hidden z-40 flex flex-row gap-3 right-4 bottom-4"> {/* Adjusted gap and z-index */}
        <Button variant="outline" size="icon" onClick={onChaptersClick} className="rounded-full h-12 w-12 shadow-lg bg-background border-border hover:bg-muted">
          <PanelLeftOpen className="h-5 w-5 text-primary" />
        </Button>
        <Button size="icon" onClick={() => setShowMobileMenu(true)} className="rounded-full h-12 w-12 shadow-lg bg-primary text-primary-foreground hover:bg-primary/90">
          <Menu className="h-5 w-5" />
        </Button>
      </div>

      <Sheet open={showMobileMenu} onOpenChange={setShowMobileMenu}>
        <SheetContent side="bottom" className="p-0 h-auto rounded-t-xl bg-card shadow-xl">
          <SheetHeader className="p-4 border-b"><SheetTitle className="text-center text-base font-semibold">Menu Options</SheetTitle></SheetHeader>
          {user && (<div className="px-4 pt-4 pb-2"><MagicEditCreditsDisplay variant="default" /></div>)} {/* Adjusted padding */}
          <div className="p-4 grid grid-cols-4 gap-x-2 gap-y-4"> {/* Adjusted gap */}
            {[
              { icon: <Save className="h-5 w-5" />, label: "Save", onClick: handleSave, disabled: false },
              { icon: <PanelLeftOpen className="h-5 w-5" />, label: "Chapters", onClick: onChaptersClick, disabled: false },
              { icon: <Download className="h-5 w-5" />, label: "Export", onClick: () => setShowExportDialog(true), disabled: false },
              { icon: isCreatingChapter ? <Loader2 className="h-5 w-5 animate-spin" /> : <ArrowRight className="h-5 w-5" />, label: isCreatingChapter ? "Creating..." : "New Chapter", onClick: async () => { if (onNewChapter && !isCreatingChapter) { setIsCreatingChapter(true); try { await onNewChapter(); } finally { setIsCreatingChapter(false); setShowMobileMenu(false); } } }, disabled: isCreatingChapter },
              { icon: isFullView ? <Minimize className="h-5 w-5" /> : <Maximize className="h-5 w-5" />, label: isFullView ? "Normal" : "Full View", onClick: toggleFullView, disabled: false },
              { icon: <Wand2 className="h-5 w-5" />, label: "Templates", onClick: () => setShowTemplateDialog(true), disabled: false },
              { icon: isGeneratingAiContent ? <Loader2 className="h-5 w-5 animate-spin" /> : <Sparkles className="h-5 w-5" />, label: "Magic Edit™", onClick: () => { onTriggerMagicEdit(); setShowMobileMenu(false); }, disabled: !isRewritePossible || isGeneratingAiContent, className: "text-amber-500 font-medium" },
              { icon: isSoundOn ? <Volume2 className="h-5 w-5" /> : <VolumeX className="h-5 w-5" />, label: "Sound", onClick: toggleSound, disabled: false },
              { icon: isGeneratingAiContent ? <Loader2 className="h-5 w-5 animate-spin" /> : <Brain className="h-5 w-5" />, label: "BookMemory™", onClick: () => { setShowBookMemoryDrawer(true); setShowMobileMenu(false); }, disabled: isGeneratingAiContent, className: "text-amber-500" },
              { icon: <Settings className="h-5 w-5" />, label: "Settings", onClick: () => setShowSettingsDialog(true), disabled: false },
              { icon: <LogOut className="h-5 w-5" />, label: "Logout", onClick: handleLogout, disabled: false, className: "text-red-500" },
            ].map(item => (
              <Button key={item.label} variant="ghost" className={cn("flex flex-col items-center justify-center h-auto py-3 space-y-1.5 text-xs leading-tight", item.className)} onClick={item.onClick} disabled={item.disabled}>
                {item.icon}
                <span>{item.label}</span>
              </Button>
            ))}
          </div>
        </SheetContent>
      </Sheet>

      <TemplateSelectorDialog open={showTemplateDialog} onOpenChange={setShowTemplateDialog} projectId={projectId} />
      <ExportDialog open={showExportDialog} onOpenChange={setShowExportDialog} projectId={projectId} projectTitle={projectTitle} />
      <ProjectSettingsDialog 
        open={showSettingsDialog} 
        onOpenChange={setShowSettingsDialog} 
        projectId={projectId} 
        project={{
          id: project.id,
          title: project.title,
          author: project.author || '',
          cover_image_url: project.cover_image_url,
          word_goal: project.word_goal
        }} 
        onProjectUpdate={onProjectUpdate} 
      />
      <BookMemoryDrawer open={showBookMemoryDrawer} onOpenChange={setShowBookMemoryDrawer} projectId={projectId} />
    </>
  );
}
