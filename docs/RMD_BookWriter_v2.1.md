## 🧠 RMD: BookWriter – The Ultimate Writing Platform (v2.1 - Detailed for AI Implementation with shadcn/ui)

**Objective:** This document details the requirements for building the front-end of BookWriter using Next.js, TypeScript, Tailwind CSS, and **`shadcn/ui`**. It's intended to be parsed by an AI code generation tool (like <PERSON>) to scaffold the application structure, components, and core logic. Back-end interactions will primarily use Supabase.

**Target Audience:** AI Code Generator (Claude), Front-End Developers.

**Assumptions:**
*   The project is initialized using the stack defined in the provided `package.json`.
*   `shadcn/ui` components will be used extensively for building the UI. Developers should use the `shadcn-cli` to add components as needed (e.g., `npx shadcn-ui@latest add button input card dialog...`).
*   Core functionality is the priority.

---

### ⚠️ 0. PREREQUISITES / SETUP

*   **Install Missing Dependencies:** Before starting, ensure the following core libraries are installed:
    ```bash
    npm install @supabase/supabase-js
    npm install @tiptap/react @tiptap/pm @tiptap/starter-kit # For the editor
    npm install react-beautiful-dnd @types/react-beautiful-dnd # For chapter reordering
    # Install export libraries later when implementing the feature
    # npm install html-docx-js file-saver jszip @types/file-saver
    # npm install jspdf html2canvas
    # npm install epub-gen # (May require server-side handling)
    # Install PWA library later if needed
    # npm install next-pwa
    ```
*   **Initialize Supabase Client:** Create a utility file (`lib/supabase/client.ts` or similar) to initialize and export the Supabase client using environment variables for the URL and anon key.
*   **Setup `shadcn/ui`:** Ensure `shadcn/ui` is configured correctly (components path, utils, etc.). Add necessary components using the CLI:
    ```bash
    npx shadcn-ui@latest add button input card label dialog alert-dialog sheet dropdown-menu tooltip toast sonner form select # Add others as needed
    ```
*   **Setup `react-hook-form`:** Integrate with `shadcn/ui`'s `Form` component as per `shadcn/ui` documentation.

---

### ⚙️ 1. TECHNOLOGY STACK (Confirmed/Refined)

*   **Framework:** Next.js (v15+ App Router)
*   **Language:** TypeScript
*   **Styling:** Tailwind CSS
*   **UI Components:** **`shadcn/ui`** (leveraging Radix UI primitives and `lucide-react` icons)
*   **Backend:** Supabase (Auth, Database) - Use `@supabase/supabase-js`.
*   **State Management:** React Context API / `useState`. Zustand/Jotai if needed later.
*   **Rich Text Editor:** TipTap (v2) with `StarterKit` (Needs installation).
*   **Forms:** `react-hook-form` with `zod` (integrated with `shadcn/ui` Form components).
*   **Icons:** `lucide-react` (Included via `shadcn/ui`).
*   **Notifications:** `sonner` (Integrated via `shadcn/ui`).
*   **Drawers/Sheets:** `vaul` (Integrated via `shadcn/ui`'s `Sheet` component).
*   **PWA:** `next-pwa` (Needs installation and configuration).

---

### ✅ 2. CORE REQUIREMENTS

#### 🔐 2.1 Authentication (Web + Mobile Responsive)

*   **Routes:** `/login`, `/signup`.
*   **Component Structure:**
    *   `AuthLayout`: (`app/(auth)/layout.tsx`) - Dark background (`bg-[#0F0F0F] min-h-screen flex items-center justify-center`).
    *   `LoginForm`: (`app/(auth)/login/page.tsx`).
    *   `SignupForm`: (`app/(auth)/signup/page.tsx`).
*   **Design & Styling (`shadcn/ui` & Tailwind):**
    *   **Card:** Use `shadcn/ui` `Card` component (`Card`, `CardHeader`, `CardTitle`, `CardContent`, `CardFooter`). Center it (`max-w-sm w-full`).
    *   **Form:** Use `shadcn/ui` `Form` component integrated with `react-hook-form` and `zod`.
    *   **Inputs:** Use `shadcn/ui` `Input` component within `FormField` controls. Include `Label` (can be visually hidden if needed). Apply `font-serif` to input text and placeholder (`className="font-serif placeholder:font-serif"`).
    *   **Button:** Use `shadcn/ui` `Button` component. Full width (`w-full`). Style: Primary (`variant="default"` - likely black bg by default in `shadcn/ui`, adjust theme if needed), `font-serif`. Add `disabled` state based on form state (`isLoading` or `!form.formState.isValid`).
    *   **Footer Toggle:** Use `next/link` within the `CardFooter`. Style: `text-sm text-muted-foreground`. Link: `hover:underline`.
*   **Functionality:**
    *   Use `react-hook-form` + `zod` schema for `email`, `password`.
    *   **Supabase Interaction:** `supabase.auth.signInWithPassword` / `signUp`.
    *   **Error Handling:** Use `form.setError` or display general errors using `shadcn/ui` `Alert` or `AlertDescription`. Use `sonner` (`toast.error()`) for feedback.
    *   **Loading State:** Show loading indicator within the `Button` (e.g., replace text/icon with `Loader2` icon from `lucide-react` with `animate-spin`).
    *   **Redirect:** `useRouter().push('/dashboard')`.
    *   **Auth Listener:** Setup `onAuthStateChange` listener in root layout or provider.

#### 🏠 2.2 Dashboard ("The Pencil Sharpener")

*   **Route:** `/dashboard` (Protected).
*   **Component Structure:**
    *   `DashboardPage`: (`app/dashboard/page.tsx`).
    *   `DashboardOptionCard`: Reusable component for options.
*   **Layout:**
    *   Container (`max-w-4xl mx-auto px-4 py-8`).
    *   Header: `h1` (`font-serif text-3xl font-bold mb-2`), `p` (`text-muted-foreground mb-8`).
    *   Options Grid (`grid grid-cols-1 md:grid-cols-3 gap-6`).
*   **Options (`DashboardOptionCard`):** Use `shadcn/ui` `Card` component.
    *   **➕ New Project:**
        *   Icon: `PlusCircle` (`lucide-react`, size `h-10 w-10 mb-3`).
        *   Text: `CardTitle`.
        *   Action: Wrap card in `Link` from `next/link` (`href="/dashboard/new"`) or use `onClick` with `router.push`. Make card interactive (`hover:shadow-lg transition`).
    *   **📚 Existing Projects:**
        *   Icon: `BookOpen` (`lucide-react`). Text: `CardTitle`. Action: Link to `/dashboard/projects`.
    *   **⬆️ Import Document:**
        *   Icon: `FileUp` (`lucide-react`). Text: `CardTitle`. Action: Link/Button (implement later).
*   **Card Styling (`DashboardOptionCard`):** `shadcn/ui` `Card` with `CardHeader`, `CardContent`. Center content (`flex flex-col items-center text-center`).

#### 📝 2.3 New Project Flow

*   **Route:** `/dashboard/new` (Protected).
*   **Component Structure:**
    *   `NewProjectPage`: (`app/dashboard/new/page.tsx`).
    *   `NewProjectForm`: Component using `shadcn/ui` `Form`.
*   **Form Fields (`shadcn/ui` `Form`, `FormField`, `Label`, `Input`):**
    *   `title`: Text input, required.
    *   `author`: Text input, required.
    *   `word_goal`: Number input, optional (`type="number"`).
*   **Styling:** Standard form layout within a centered `Card`.
*   **Button:** `shadcn/ui` `Button`. Text: "Create Project".
*   **Functionality:**
    *   Validate using `zod` schema.
    *   Show loading state on button (`Loader2` icon).
    *   **Supabase Interaction:** `insert` into `projects`, then `insert` into `chapters` for "Chapter 1".
    *   Handle errors using `sonner` (`toast.error`).
    *   Redirect: `router.push(`/project/${newProject.id}`)`.

#### 📁 2.4 Existing Projects

*   **Route:** `/dashboard/projects` (Protected).
*   **Component Structure:**
    *   `ProjectsListPage`: (`app/dashboard/projects/page.tsx`).
    *   `ProjectCard`: Component using `shadcn/ui` `Card`.
    *   `DeleteConfirmationDialog`: Use `shadcn/ui` `AlertDialog`.
*   **Layout:**
    *   Header: "Your Projects".
    *   Project List: Grid (`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4`).
    *   Loading State: Use `shadcn/ui` `Skeleton` components matching the `ProjectCard` structure.
    *   Empty State: Message with a `Button` or `Link` to `/dashboard/new`.
*   **Data Fetching:** Fetch projects for the user from Supabase.
*   **Project Card (`ProjectCard` - using `shadcn/ui` `Card`):**
    *   `CardHeader`: `CardTitle` (Project Title), `CardDescription` (Author Name).
    *   `CardContent`: Display formatted `updated_at` (use `date-fns` - `formatDistanceToNow`).
    *   `CardFooter`: `flex justify-between`.
        *   **Open:** `Button` (`variant="default"`). Use `Link` component wrapping the button, `href={`/project/${project.id}`}`. Text: "Open".
        *   **Delete:** `Button` (`variant="destructive"`). `onClick` triggers the `DeleteConfirmationDialog`. Text: "Delete".
*   **Delete Confirmation (`DeleteConfirmationDialog` - using `AlertDialog`):**
    *   `AlertDialogTrigger` is the Delete button.
    *   `AlertDialogContent`: Title, Description ("Are you sure... Type DELETE...").
    *   `Input` field for typing "DELETE". State to track input value.
    *   `AlertDialogFooter`: `AlertDialogCancel`, `AlertDialogAction` (Confirm Delete). Disable Confirm button until input matches "DELETE".
    *   **On Confirm (`AlertDialogAction` `onClick`):** Call Supabase delete, show loading state, handle errors (`toast.error`), show success (`toast.success`), close dialog, refresh list.

#### 🧠 2.5 Writing Desk (Core Editor)

*   **Route:** `/project/[projectId]` (Protected).
*   **Component Structure:**
    *   `WritingDeskPage`: Main component (`app/project/[projectId]/page.tsx`).
    *   `EditorPanel`: Central editor component.
    *   `ChapterSheet`: Left sidebar using `shadcn/ui` `Sheet`.
    *   `MainToolbar`: Bottom toolbar.
    *   `WordCountBadge`: Small display component.
    *   `TemplateSelectorDialog`: Use `shadcn/ui` `Dialog`.
    *   `ExportDialog`: Use `shadcn/ui` `Dialog`.
*   **Data Fetching:** Fetch project and ordered chapters. Store in state.
*   **Layout:** Main area centers `EditorPanel`. `ChapterSheet` slides from left. `MainToolbar` floats at bottom.
*   **Editor Panel (`EditorPanel`):**
    *   **Container Styling:** `bg-background text-foreground rounded-lg shadow-lg mx-auto my-8 p-8 md:p-12 relative max-w-2xl w-[calc(100%-32px)]`. Use `prose` class from `@tailwindcss/typography` (install if needed) for basic styling, or define custom styles.
    *   **TipTap Editor:**
        *   Initialize `useEditor` (`@tiptap/react`). Extensions: `StarterKit`, `Placeholder`.
        *   Content: Load active chapter HTML.
        *   **Typography:** Apply `font-serif` (e.g., 'Merriweather') via Tailwind class on the container or editor props. Ensure font is loaded.
        *   **Autosave:** Debounced save on `onUpdate`, immediate save on `onBlur`. Update Supabase `chapters` and project `updated_at`. Use `sonner` (`toast`) for subtle save status feedback (optional). Log to `timeline` table.
    *   **Pagination Overlay (`WordCountBadge`):**
        *   Use `shadcn/ui` `Badge` component (`variant="secondary"`). Position top-right.
        *   Calculate words/pages, update on editor changes.
*   **Toolbar (`MainToolbar`):**
    *   **Desktop:** Floating bar (`fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50`). Use `shadcn/ui` `Card` or a simple `div` with `bg-muted rounded-full p-2 flex gap-1`.
    *   **Mobile:** Use `shadcn/ui` `Sheet` triggered by a button. Set `side="bottom"`.
    *   **Toggle:** A small persistent `Button` (`variant="ghost"`, `size="icon"`) with `PanelBottomOpen` or `Menu` icon from `lucide-react` to toggle visibility/open the sheet.
    *   **Actions (Use `shadcn/ui` `Button` with `variant="ghost"` or `outline"`, `size="icon"` inside the toolbar/sheet. Wrap in `Tooltip` for desktop):**
        *   `💾 Save`: Icon: `Save`. Trigger manual save.
        *   `📑 Chapters`: Icon: `PanelLeftOpen`. Trigger `ChapterSheet`.
        *   `⬇️ Export`: Icon: `Download`. Trigger `ExportDialog`.
        *   `🔍 Toggle Full View`: Icon: `Maximize` / `Minimize`. (Handles sidebar visibility if needed).
        *   `🚪 Logout`: Icon: `LogOut`. Trigger `supabase.auth.signOut()`.
        *   `🎯 Word Goal Tracker`: Icon: `Target`. Display text/progress.
        *   `🖋️ Template Selector`: Icon: `Palette` or `Wand2`. Trigger `TemplateSelectorDialog`.
        *   `🔊 Ambient Sound`: Icon: `Volume2` / `VolumeX`. Toggle sound.
        *   `🕒 Timeline`: Icon: `Clock`. Trigger timeline view (Modal/Sheet).
        *   `🔗 Share Link`: Icon: `Share2`. (Future).

#### 📚 2.6 Chapter Sidebar/Sheet

*   **Component:** `ChapterSheet`. Use `shadcn/ui` `Sheet`.
*   **Trigger:** "Chapters" button in `MainToolbar`.
*   **Layout:** `Sheet` component with `side="left"`. `SheetHeader`, `SheetTitle` ("Chapters"), `SheetContent`.
*   **Content:**
    *   Scrollable list (`ScrollArea` from `shadcn/ui`). Map chapters.
    *   Each Chapter Item: `div` or `Button` (`variant="ghost"`, `w-full justify-start`). Highlight active chapter (`variant="secondary"`). `onClick` switches active chapter, closes sheet.
    *   **Actions per Chapter (e.g., on hover or via a `DropdownMenu` triggered by an ellipsis icon):**
        *   **Rename:** Use `AlertDialog` or inline input triggered by `Pencil` icon. Update Supabase `chapters`.
        *   **Delete:** Use `AlertDialog` triggered by `Trash2` icon. Update Supabase `chapters`. Refresh list.
    *   **Drag to Reorder:**
        *   Use `react-beautiful-dnd` (Needs installation). Wrap list and items.
        *   On drag end: Update local state, batch update Supabase `chapters` order.
    *   **Add New Chapter Button:** `Button` at the bottom of the sheet (`variant="outline"`). Icon: `PlusCircle`. `onClick` adds chapter to Supabase, updates UI.

#### 🎨 2.7 Templates

*   **Component:** `TemplateSelectorDialog`. Use `shadcn/ui` `Dialog`.
*   **Trigger:** "Template Selector" button in toolbar.
*   **Layout:** `DialogContent`. Grid of preview cards (`Card`).
*   **Functionality:**
    *   State for `currentTemplate`. Store in `localStorage` or `projects` table.
    *   Clicking a preview card updates the state/storage.
    *   Apply styles using CSS variables or conditional Tailwind classes on `EditorPanel`.

#### 🧾 2.8 Export

*   **Component:** `ExportDialog`. Use `shadcn/ui` `Dialog`.
*   **Trigger:** "Export" button in toolbar.
*   **Layout:** `DialogContent`. Options (e.g., `RadioGroup` from `shadcn/ui`) for EPUB, PDF, DOCX. Export `Button`.
*   **Functionality:**
    *   Fetch all ordered chapter content.
    *   Combine HTML.
    *   **Convert & Download (Needs Libraries Installed):**
        *   **DOCX:** `html-docx-js` + `file-saver`.
        *   **PDF:** `jsPDF` + `html2canvas` (client-side) or serverless function (recommended).
        *   **EPUB:** `epub-gen` (likely serverless) or simpler client-side alternative.
    *   Show loading state on button. Use `sonner` for feedback.

#### 🛜 2.9 PWA Setup

*   **Package:** Install and configure `next-pwa`.
*   **`manifest.json` / `next.config.js`:** Configure as before.
*   **Service Worker:** Configure caching strategies. Implement offline storage for last opened project (Cache API, `localStorage`) and potentially unsaved changes (IndexedDB). Add offline indicator UI.

---

### 🧩 3. COMPONENT SUMMARY (`shadcn/ui` focus)

*   **Auth:** `Card`, `Form`, `Input`, `Label`, `Button`, `Alert`.
*   **Dashboard:** `Card`, `Link`, `Button`.
*   **Projects:** `Card`, `Button`, `AlertDialog`, `Skeleton`, `DropdownMenu` (optional).
*   **Writing Desk:** `Sheet` (Chapters), `Dialog` (Export, Template), `Button`, `Tooltip`, `Badge`, `ScrollArea` (in Sheet), `RadioGroup` (in Export).
*   **Shared:** Leverage `shadcn/ui` components wherever applicable. Use `sonner` for toasts.

---

### 🔁 4. SUPABASE SCHEMA (Unchanged, RLS crucial)

*   `users`
*   `projects` (Add `template_name` text column)
*   `chapters` (Add `updated_at` timestamptz column)
*   `timeline` (Add `user_id` uuid column)
*   **Action:** Ensure RLS policies are implemented correctly.

---

### 🔄 5. SUPABASE INTERACTION PATTERNS (Unchanged)

*   Use `@supabase/supabase-js` client. Standard CRUD operations. Handle errors. Consider helper hooks.

---

### 🧭 6. STATE MANAGEMENT (Unchanged)

*   `useState`, `useContext`. Lift state as needed. `WritingDeskPage` holds core editor state.

---

### ✨ 7. BEHAVIOR & UX NOTES (Refined)

*   **Autosave:** Use `sonner` (`toast`) for subtle feedback.
*   **Minimal UI:** Leverage `shadcn/ui` components' clean aesthetic. Use `Sheet`, `Dialog`, `Popover`, `Tooltip` effectively.
*   **Mobile UX:** Use `Sheet` (from `vaul`) for bottom toolbar/drawers. Implement tap zones for chapter navigation. Ensure `shadcn/ui` components are responsive.

---

### ⚠️ 8. ERROR HANDLING & LOADING STATES (Refined)

*   **Loading:** Use `Skeleton` for content, `Loader2` icon (`lucide-react`, `animate-spin`) in buttons.
*   **Errors:** Use `AlertDialog` for critical confirmations, `sonner` (`toast.error`, `toast.success`) for feedback and non-blocking errors. Use `Form` component's error display.

---

### 🎯 9. DELIVERABLES CHECKLIST (Refined for `shadcn/ui`)

*   [ ] **Setup:** Project ready with `shadcn/ui`, Supabase client, required libs installed (TipTap, DnD).
*   [ ] **Authentication:** `shadcn/ui` forms, validation, Supabase auth, redirects.
*   [ ] **Dashboard:** `shadcn/ui` Cards for options.
*   [ ] **New Project:** `shadcn/ui` Form, Supabase inserts, redirect.
*   [ ] **Existing Projects:** `shadcn/ui` Cards, `AlertDialog` for delete, `Skeleton` loaders.
*   [ ] **Writing Desk:**
    *   [ ] Fetch data.
    *   [ ] `EditorPanel` with TipTap.
    *   [ ] Autosave with Supabase update.
    *   [ ] `Badge` for word count.
    *   [ ] `MainToolbar` (floating / `Sheet`) with `Button`+`Tooltip`/`Icon`.
*   [ ] **Chapter Sheet:**
    *   [ ] `Sheet` component.
    *   [ ] List/switch chapters.
    *   [ ] Add/Rename/Delete (`AlertDialog`).
    *   [ ] Drag-and-drop (`react-beautiful-dnd`).
*   [ ] **Export:** `Dialog` with options, basic DOCX export (`html-docx-js`).
*   [ ] **Templates:** `Dialog` applying style changes.
*   [ ] **PWA:** (If installed) Basic setup.
*   [ ] **Responsiveness & UX:** `shadcn/ui` components responsive, mobile gestures functional.
*   [ ] **Database:** Schema updated, RLS configured.

---
