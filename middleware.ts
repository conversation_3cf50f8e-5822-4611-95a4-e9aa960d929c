// middleware.ts
import { NextResponse, type NextRequest } from 'next/server'
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { Database } from '@/lib/supabase/database.types'

export async function middleware(request: NextRequest) {
  // Skip API routes to prevent excessive auth checks
  if (request.nextUrl.pathname.startsWith('/api/')) {
    return NextResponse.next();
  }

  // 1️⃣ Prepare a response so Supabase can set cookies if needed
  const response = NextResponse.next()

  // 2️⃣ Init Supabase middleware client (auto-wires cookies/headers)
  const supabase = createMiddlewareClient<Database>(
    { req: request, res: response },
    {
      options: {
        autoRefreshToken: false, // Disable auto refresh to prevent redirects
        persistSession: true
      }
    }
  )

  // 3️⃣ Get the current session
  const {
    data: { session },
  } = await supabase.auth.getSession()

  // 4️⃣ Route-guard logic - Only handle authorization cases
  const isProtected = request.nextUrl.pathname.startsWith('/dashboard') ||
                      request.nextUrl.pathname.startsWith('/project') ||
                      request.nextUrl.pathname.startsWith('/profile')
  const isAuth      = ['/login', '/signup'].includes(request.nextUrl.pathname)

  // Only redirect if not authenticated on protected routes
  if (isProtected && !session) {
    console.log('Middleware: Redirecting to login - No session found')
    const url = new URL('/login', request.url)
    url.searchParams.set('redirect', request.nextUrl.pathname)
    return NextResponse.redirect(url)
  }

  // Only redirect if authenticated on auth routes
  if (isAuth && session) {
    console.log('Middleware: Redirecting to dashboard - User already authenticated')
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }

  // 5️⃣ Return the response without modification for all other cases
  return response
}

export const config = {
  matcher: [
    '/dashboard/:path*',
    '/project/:path*',
    '/profile/:path*',
    '/login',
    '/signup',
  ],
}
