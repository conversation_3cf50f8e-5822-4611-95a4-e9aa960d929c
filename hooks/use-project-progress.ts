// This is the OLD, problematic version of useProjectProgress.
// It should NOT be used. Please use use-project-progress-fixed.ts instead.
// If this file is being imported, it will cause errors.
export function useProjectProgress(projects: any, userId: string | undefined) {
  console.error("ERROR: Attempting to use the OLD useProjectProgress hook from hooks/use-project-progress.ts. Please update imports to use hooks/use-project-progress-fixed.ts");
  return { projectProgress: {}, loading: true };
}

// Intentionally throw an error if this module is loaded.
if (typeof window !== 'undefined') {
  // Ensure this runs only in the browser environment where it causes issues.
  // throw new Error("OLD useProjectProgress.ts loaded. This is an error. Update imports!");
  // Softer error for now:
  console.error("OLD useProjectProgress.ts (hooks/use-project-progress.ts) was loaded. This indicates an incorrect import somewhere in the application. Please find and fix it.");
}
