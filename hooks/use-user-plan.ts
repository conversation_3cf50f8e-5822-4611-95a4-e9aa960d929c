'use client';

import { useCredits } from '@/lib/hooks/use-credits';

export type UserPlan = 'free' | 'paid';

interface UserPlanDetails {
  plan: UserPlan;
  isPaidUser: boolean;
  magicEditsUsed: number;
  magicEditLimit: number;
  magicEditsRemaining: number;
  bookMemoryEnabled: boolean;
  isLoading: boolean;
  isError: boolean;
  refetch: () => Promise<void>;
  notifyMagicEditCompleted: () => boolean;
}

/**
 * Enhanced hook for user plan details - uses our improved SWR-based credit hook
 * This version leverages the more robust caching and error handling in useCredits
 */
export function useUserPlan(userId: string | undefined): UserPlanDetails {
  // Use the enhanced credits hook for efficient data fetching with improved caching
  const { 
    credits, 
    isLoading, 
    error, 
    refreshCredits,
    notifyMagicEditCompleted,
    isPaidUser,
    magicEditsUsed,
    magicEditLimit,
    magicEditsRemaining
  } = useCredits(userId);
  
  // Format the response to match the original interface but with new utilities
  return {
    plan: credits.plan,
    isPaidUser,
    magicEditsUsed,
    magicEditLimit,
    magicEditsRemaining,
    bookMemoryEnabled: credits.bookMemoryEnabled,
    isLoading,
    isError: !!error,
    // Higher-level functions from the enhanced hook
    // Force refresh after Magic Edit to ensure immediate update
  refetch: () => refreshCredits(true), 
  notifyMagicEditCompleted
  };
}

/**
 * Enhanced hook to enforce Magic Edit™ limits
 * Now uses the canUseMagicEdit function directly from useCredits
 * to ensure consistent behavior and reduce duplicated code
 */
export function useMagicEditLimiter(
  userId: string | undefined,
  userPlanDetails?: Omit<UserPlanDetails, 'refetch' | 'notifyMagicEditCompleted'>
): {
  canUseMagicEdit: () => boolean;
  magicEditsRemaining: number;
} {
  // CRITICAL FIX: We avoid conditional hook calls with this pattern
  // If userPlanDetails is provided, we use it, otherwise we call useCredits directly
  // (Previously we called useUserPlan which then called useCredits)
  
  // Optimized path: If we already have userPlanDetails, use them
  if (userPlanDetails) {
    // Simpler implementation that just returns the values from userPlanDetails
    return {
      magicEditsRemaining: userPlanDetails.magicEditsRemaining,
      // In this case we need to manually implement canUseMagicEdit
      canUseMagicEdit: () => {
        // Use the built-in credits checker from useCredits directly
        const { canUseMagicEdit } = useCredits(userId); 
        return canUseMagicEdit();
      }
    };
  }
  
  // Optimized path: Get everything directly from useCredits
  const { 
    canUseMagicEdit, 
    magicEditsRemaining 
  } = useCredits(userId);
  
  return { canUseMagicEdit, magicEditsRemaining };
}

/**
 * Enhanced hook to check if BookMemory™ is available for the user
 */
export function useBookMemoryAvailability(
  userId: string | undefined,
  userPlanDetails?: Omit<UserPlanDetails, 'refetch' | 'notifyMagicEditCompleted'>
): {
  isBookMemoryEnabled: boolean;
  isPaidUser: boolean;
} {
  // CRITICAL FIX: We avoid conditional hook calls with this pattern
  
  // Optimized path: If we already have userPlanDetails, use them
  if (userPlanDetails) {
    return {
      isBookMemoryEnabled: userPlanDetails.bookMemoryEnabled,
      isPaidUser: userPlanDetails.isPaidUser
    };
  }
  
  // Optimized path: Get everything directly from useCredits
  const { credits } = useCredits(userId);
  
  return { 
    isBookMemoryEnabled: credits.bookMemoryEnabled, 
    isPaidUser: credits.isPaidUser 
  };
}
