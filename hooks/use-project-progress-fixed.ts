"use client"

import { useState, useEffect, useRef, useCallback } from 'react'
import { supabase } from '@/lib/supabase/client'
import { Book, Chapter, BookProgress } from '@/lib/types'

interface ProjectProgress {
  [projectId: string]: BookProgress
}

/**
 * NUCLEAR FIX: Completely rewritten hook with no real-time subscriptions
 * 
 * This hook provides project progress tracking without using Supabase real-time
 * subscriptions, which were causing infinite loops and browser crashes.
 */
export function useProjectProgress(projects: Book[], userId: string | undefined) {
  // State for tracking progress and loading status
  const [projectProgress, setProjectProgress] = useState<ProjectProgress>({})
  const [loading, setLoading] = useState(true)
  
  // Ref to track if the component is mounted
  const isMounted = useRef(true)
  
  // Ref to track fetch attempts and prevent excessive fetching
  const fetchAttemptsRef = useRef(0)
  const lastFetchTimeRef = useRef(0)
  const MAX_FETCH_ATTEMPTS = 3
  const FETCH_COOLDOWN_MS = 5000 // 5 seconds
  
  // Ref to store cached data
  const cachedDataRef = useRef<ProjectProgress>({})
  
  /**
   * Calculate progress for a specific project based on its chapters
   */
  const calculateProjectProgress = (project: Book, chapters: Chapter[]): BookProgress => {
    // Calculate total word count across all chapters
    const calculatedWordCount = chapters.reduce((sum, chapter) => {
      // Ensure we handle null/undefined word counts
      const chapterWordCount = typeof chapter.word_count === 'number' ? chapter.word_count : 0
      return sum + chapterWordCount
    }, 0)
    
    // Use project goal or default to 100,000 words
    const goalWords = project.goal || 100000
    
    // Calculate progress percentage, capped at 100%
    const progressPercentage = Math.min(Math.round((calculatedWordCount / goalWords) * 100), 100)
    
    return {
      calculated_word_count: calculatedWordCount,
      goal: goalWords,
      progress_percentage: progressPercentage
    }
  }
  
  /**
   * Fetch project data with throttling to prevent excessive API calls
   */
  const fetchProjectData = async () => {
    if (!userId || projects.length === 0) {
      setLoading(false)
      return
    }
    
    // NUCLEAR THROTTLING: Check if we've exceeded the maximum number of attempts
    const now = Date.now()
    const timeSinceLastFetch = now - lastFetchTimeRef.current
    
    // If we've tried too many times in a short period, use cached data
    if (fetchAttemptsRef.current >= MAX_FETCH_ATTEMPTS && 
        timeSinceLastFetch < FETCH_COOLDOWN_MS) {
      console.log(`[useProjectProgress] Too many fetch attempts (${fetchAttemptsRef.current}). Using cached data.`)
      
      // If we have cached data, use it
      if (Object.keys(cachedDataRef.current).length > 0) {
        setProjectProgress(cachedDataRef.current)
      }
      
      setLoading(false)
      return
    }
    
    // If enough time has passed, reset the counter
    if (timeSinceLastFetch >= FETCH_COOLDOWN_MS) {
      fetchAttemptsRef.current = 0
    }
    
    // Update the last fetch time and increment the counter
    lastFetchTimeRef.current = now
    fetchAttemptsRef.current++
    
    try {
      setLoading(true)
      
      // Get valid project IDs
      const projectIds = projects
        .filter(p => p && p.id)
        .map(p => p.id)
      
      if (projectIds.length === 0) {
        console.log('[useProjectProgress] No valid project IDs found')
        setLoading(false)
        return
      }
      
      console.log('[useProjectProgress] Fetching chapters for projects:', projectIds.join(', '))
      
      // NUCLEAR FIX: Use a simple fetch with error handling
      try {
        // Fetch all chapters for all projects in one query
        const { data: allChapters, error } = await supabase
          .from('chapters')
          .select('*')
          .in('project_id', projectIds as any)
        
        if (error) {
          throw error
        }
        
        if (!allChapters) {
          throw new Error('No chapters data returned')
        }
        
        console.log(`[useProjectProgress] Found ${allChapters.length} chapters across all projects`)
        
        // Group chapters by project_id
        const chaptersByProject: { [projectId: string]: Chapter[] } = {}
        allChapters.forEach((chapter: any) => {
          if (!chaptersByProject[chapter.project_id]) {
            chaptersByProject[chapter.project_id] = []
          }
          chaptersByProject[chapter.project_id].push(chapter as Chapter)
        })
        
        // Calculate progress for each project
        const progress: ProjectProgress = {}
        projects.forEach(project => {
          if (!project || !project.id) return
          
          const projectChapters = chaptersByProject[project.id] || []
          progress[project.id] = calculateProjectProgress(project, projectChapters)
          
          console.log(
            `[useProjectProgress] Project ${project.id}: ` +
            `${progress[project.id].calculated_word_count} words, ` +
            `${progress[project.id].progress_percentage}% of goal`
          )
        })
        
        // Update state and cache if component is still mounted
        if (isMounted.current) {
          setProjectProgress(progress)
          cachedDataRef.current = progress // Cache the data
          fetchAttemptsRef.current = 0 // Reset attempt counter on success
        }
      } catch (error) {
        console.error('[useProjectProgress] Error fetching chapters:', error)
        
        // If we have cached data, use it as a fallback
        if (Object.keys(cachedDataRef.current).length > 0) {
          console.log('[useProjectProgress] Using cached data as fallback')
          setProjectProgress(cachedDataRef.current)
        }
      }
    } catch (error) {
      console.error('[useProjectProgress] Unexpected error:', error)
    } finally {
      if (isMounted.current) {
        setLoading(false)
      }
    }
  }
  
  // Memoize fetchProjectData to prevent unnecessary re-renders
  const fetchProjectDataMemoized = useCallback(fetchProjectData, [userId, projects]);
  
  // Track if initial fetch has been done
  const initialFetchDoneRef = useRef(false)
  
  // Effect to load data on mount and when projects change
  useEffect(() => {
    // Reset mounted ref
    isMounted.current = true
    
    // FIXED: Only fetch data if we haven't done the initial fetch yet
    // or if the projects or userId have changed
    if (!initialFetchDoneRef.current) {
      console.log('[useProjectProgress] Performing initial data fetch')
      fetchProjectDataMemoized()
      initialFetchDoneRef.current = true
    } else {
      console.log('[useProjectProgress] Skipping redundant fetch on re-render')
    }
    
    // Set up a polling interval instead of real-time subscription
    // This is more reliable and won't cause infinite loops
    const intervalId = setInterval(() => {
      if (isMounted.current) {
        console.log('[useProjectProgress] Polling for updates')
        fetchProjectDataMemoized()
      }
    }, 60000) // Poll every 60 seconds (increased from 30s to reduce API load)
    
    // Cleanup function
    return () => {
      isMounted.current = false
      clearInterval(intervalId)
    }
  }, [userId, projects, fetchProjectDataMemoized])
  
  return { projectProgress, loading }
}
