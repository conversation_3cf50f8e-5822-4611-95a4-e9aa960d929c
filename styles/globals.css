@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 0%;
    --foreground: 0 0% 92%;

    --card: 0 0% 0%;
    --card-foreground: 0 0% 92%;

    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 92%;

    --primary: 45 100% 55%;
    --primary-foreground: 0 0% 0%;

    --secondary: 0 0% 0%;
    --secondary-foreground: 0 0% 92%;

    --muted: 0 0% 0%;
    --muted-foreground: 0 0% 70%;

    --accent: 45 100% 55%;
    --accent-foreground: 0 0% 0%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 15%;
    --input: 0 0% 15%;
    --ring: 45 100% 55%;

    --radius: 0.5rem;
    
    --sidebar-background: 0 0% 0%;
    --sidebar-foreground: 0 0% 92%;
    --sidebar-primary: 45 100% 55%;
    --sidebar-primary-foreground: 0 0% 0%;
    --sidebar-accent: 0 0% 0%;
    --sidebar-accent-foreground: 0 0% 92%;
    --sidebar-border: 0 0% 15%;
    --sidebar-ring: 45 100% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    background-color: #000 !important;
    color: #eaeaea !important;
  }
  .bg-card, .bg-popover, .bg-muted, .bg-sidebar, .bg-sidebar-accent, .bg-sidebar-border, .bg-sidebar-ring {
    background-color: #000 !important;
  }
  .text-card-foreground, .text-popover-foreground, .text-muted-foreground, .text-sidebar-foreground, .text-sidebar-accent-foreground {
    color: #eaeaea !important;
  }
  /* Overlay modals and dialogs */
  .DialogContent, .dialog-content, .modal, .sheet, .popover, .dropdown-menu, .dropdown-content {
    background-color: #000 !important;
    color: #eaeaea !important;
    border: 1px solid rgba(255,255,255,0.08);
    box-shadow: 0 8px 32px 0 rgba(0,0,0,0.8);
  }
}

.gold-gradient {
  background: linear-gradient(135deg, #ffd700 0%, #ffc107 50%, #ffaa00 100%);
}

.gold-text {
  background: linear-gradient(135deg, #ffd700 0%, #ffc107 50%, #ffaa00 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.page-transition-enter {
  opacity: 0;
  transform: translateY(10px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 500ms, transform 500ms;
}

.premium-card {
  backdrop-filter: blur(10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 215, 0, 0.1);
  transition: all 0.3s ease;
}

.premium-card:hover {
  transform: translateY(-5px);
  border: 1px solid rgba(255, 215, 0, 0.3);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.6), 0 0 20px rgba(255, 215, 0, 0.1);
}

.serif-text {
  font-family: var(--font-serif);
}

/* E-reader styles */
.e-reader {
  font-family: "Georgia", serif;
  line-height: 1.8;
  letter-spacing: 0.01em;
}

.e-reader p {
  text-indent: 1.5em;
  margin-bottom: 1.5em;
}

.e-reader p:first-of-type {
  text-indent: 0;
}

.e-reader p:first-of-type::first-letter {
  font-size: 1.5em;
  font-weight: bold;
  float: left;
  margin-right: 0.1em;
  line-height: 1;
}

/* Add this class for shadow glow effect */
.shadow-glow {
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
  transition: box-shadow 0.3s ease;
}

.shadow-glow:hover {
  box-shadow: 0 0 25px rgba(255, 215, 0, 0.5);
}

/* Gold gradient animation for progress bars */
@keyframes goldShimmer {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.bg-gold-gradient {
  background: linear-gradient(90deg, #ffd700, #ffaa00, #ffd700);
  background-size: 200% 100%;
  animation: goldShimmer 1.5s infinite;
}
