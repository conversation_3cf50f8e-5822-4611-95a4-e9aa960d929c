'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Sparkles } from 'lucide-react';
import { useCredits } from '@/lib/hooks/use-credits';

interface MagicEditCreditsDisplayProps {
  variant?: 'default' | 'minimal' | 'button' | 'compact';
  className?: string;
  onOpenUpgrade?: () => void;
}

export function MagicEditCreditsDisplay({ 
  variant = 'default',
  onOpenUpgrade
}: MagicEditCreditsDisplayProps) {
  // Use our credits hook
  const { 
    magicEditsRemaining, 
    magicEditLimit, 
    isLoading, 
    isPaidUser,
    progressPercentage,
    isLowOnCredits 
  } = useCredits();

  // Different UI variants for flexibility
  if (variant === 'minimal') {
    // Minimal version just shows the number
    return (
      <div className="text-sm flex items-center">
        <Sparkles className="h-3.5 w-3.5 mr-1.5" style={{ color: '#FFD700' }} />
        <span className="font-medium">
          {isLoading ? '...' : `${magicEditsRemaining}`}
        </span>
      </div>
    );
  }
  
  if (variant === 'button') {
    // Button version shows credits and lets user click to upgrade
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={onOpenUpgrade}
        className={`px-3 py-1.5 h-auto text-xs font-medium ${isLowOnCredits ? 'text-amber-500' : 'text-muted-foreground'} hover:text-amber-500`}
      >
        <Sparkles className="h-3.5 w-3.5 mr-1.5" style={{ color: isLowOnCredits ? '#FFD700' : 'currentColor' }} />
        <span>
          {isLoading ? '...' : `${magicEditsRemaining} / ${magicEditLimit}`}
        </span>
      </Button>
    );
  }
  
  if (variant === 'compact') {
    // Compact version for sidebar panels
    return (
      <div className="flex items-center justify-between p-2 bg-primary/5 rounded-md">
        <div className="flex items-center gap-2">
          <Sparkles className="h-4 w-4 text-amber-500" />
          <span className="text-sm font-medium">Magic Edit Credits</span>
        </div>
        <div className="flex items-center">
          <span className={`text-sm font-semibold ${isLowOnCredits ? 'text-amber-500' : ''}`}>
            {isLoading ? '...' : `${magicEditsRemaining} remaining`}
          </span>
          {!isLoading && magicEditsRemaining === 0 && (
            <button 
              onClick={() => window.open('/pricing', '_blank')}
              className="ml-2 text-xs text-amber-500 hover:text-amber-400 font-semibold"
            >
              Add More
            </button>
          )}
        </div>
      </div>
    );
  }

  // Default display with more details
  return (
    <div className="flex items-center gap-1.5 px-3 py-1.5 bg-background/30 backdrop-blur-sm rounded-md border shadow-sm">
      <Sparkles className="h-4 w-4" style={{ color: '#FFD700' }} />
      <div className="flex flex-col">
        <div className="text-xs font-semibold">Magic Edit Credits</div>
        <div className="text-xs text-muted-foreground">
          {isLoading ? (
            <span>Loading...</span>
          ) : (
            <span className={isLowOnCredits ? 'text-amber-500 font-medium' : ''}>
              {magicEditsRemaining} remaining {!isPaidUser && magicEditsRemaining === 0 && (
                <button 
                  onClick={() => window.open('/pricing', '_blank')}
                  className="ml-1.5 text-xs text-amber-500 hover:text-amber-400 font-semibold"
                >
                  Add More
                </button>
              )}
            </span>
          )}
        </div>
      </div>
      
      {isLowOnCredits && !isPaidUser && onOpenUpgrade && (
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={onOpenUpgrade}
          className="ml-2 h-7 px-2 text-xs text-amber-500 hover:text-amber-600 hover:bg-amber-100/10"
        >
          Upgrade
        </Button>
      )}
    </div>
  );
}
