"use client"

import { useState } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ipContent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Crown } from "lucide-react"
import { CheckoutButton } from "@/components/checkout-button"
import { useAuth } from "@/lib/providers/auth-provider"

export function UpgradeButton() {
  const [isHovered, setIsHovered] = useState(false)
  const { user } = useAuth()

  return (
    <div className="fixed bottom-20 right-6 z-40">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {!user ? (
              <Link href="/pricing" passHref>
                <Button
                  variant="gold"
                  size="sm"
                  className={`rounded-full px-4 py-2 shadow-[0_0_15px_rgba(255,215,0,0.3)] transition-all duration-300 ${
                    isHovered ? "shadow-[0_0_20px_rgba(255,215,0,0.6)]" : ""
                  }`}
                  onMouseEnter={() => setIsHovered(true)}
                  onMouseLeave={() => setIsHovered(false)}
                >
                  <Crown className="h-4 w-4 mr-2" />
                  Buy Magic Edit Credits
                </Button>
              </Link>
            ) : (
              <CheckoutButton
                planType="credit_25"
                size="sm"
                className={`rounded-full px-4 py-2 shadow-[0_0_15px_rgba(255,215,0,0.3)] transition-all duration-300 ${
                  isHovered ? "shadow-[0_0_20px_rgba(255,215,0,0.6)]" : ""
                }`}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
              >
                <Crown className="h-4 w-4 mr-2" />
                Buy Magic Edit Credits
              </CheckoutButton>
            )}
          </TooltipTrigger>
          <TooltipContent side="left" className="max-w-[200px]">
            <p>Purchase Magic Edit credits for AI-powered writing assistance.</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  )
}
