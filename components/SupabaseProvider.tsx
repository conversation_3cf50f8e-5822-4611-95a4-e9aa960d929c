'use client'

import { useState, PropsWith<PERSON>hildren, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { SessionContextProvider } from '@supabase/auth-helpers-react'
import { Database } from '@/lib/supabase/database.types'
import { ThemeProvider } from '@/components/theme-provider'

// Provider that creates and manages the Supabase client on the client-side
export default function SupabaseProvider({ children }: PropsWithChildren) {
  // Create the Supabase client with default configuration
  const [supabaseClient] = useState(() => 
    createClientComponentClient<Database>()
  )

  // Add error handling for client initialization
  useEffect(() => {
    // Check if the client was initialized properly
    if (!supabaseClient) {
      console.error('Failed to initialize Supabase client');
    } else {
      console.log('Supabase client initialized successfully');
    }

    // Clean up function
    return () => {
      // Nothing to clean up for now
    };
  }, [supabaseClient]);

  return (
    <SessionContextProvider supabaseClient={supabaseClient}>
      <ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
        {children}
      </ThemeProvider>
    </SessionContextProvider>
  )
}
