"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { useToast } from "@/hooks/use-toast"
import { ChevronRight, FilePlus, FileText, MoreVertical, X } from "lucide-react"

type Chapter = {
  id: string
  title: string
  wordCount: number
}

// Sample chapters data
const sampleChapters: Chapter[] = [
  { id: "chapter-1", title: "Chapter 1: The Beginning", wordCount: 2450 },
  { id: "chapter-2", title: "Chapter 2: The Journey", wordCount: 3120 },
  { id: "chapter-3", title: "Chapter 3: The Encounter", wordCount: 2870 },
  { id: "chapter-4", title: "Chapter 4: The Revelation", wordCount: 3540 },
  { id: "chapter-5", title: "Chapter 5: The Conflict", wordCount: 2680 },
]

export function EditorSidebar({ bookId }: { bookId: string }) {
  const [isOpen, setIsOpen] = useState(true)
  const [chapters, setChapters] = useState<Chapter[]>(sampleChapters)
  const [activeChapter, setActiveChapter] = useState(chapters[0]?.id)
  const { toast } = useToast()

  const addNewChapter = () => {
    const newChapter = {
      id: `chapter-${chapters.length + 1}`,
      title: `Chapter ${chapters.length + 1}: New Chapter`,
      wordCount: 0,
    }

    setChapters([...chapters, newChapter])
    setActiveChapter(newChapter.id)

    toast({
      title: "Chapter created",
      description: "New chapter has been added to your book.",
    })
  }

  if (!isOpen) {
    return (
      <div className="fixed top-20 left-0 z-30">
        <Button variant="secondary" size="sm" className="ml-2 rounded-full h-8 w-8" onClick={() => setIsOpen(true)}>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    )
  }

  return (
    <div className="fixed top-16 left-0 bottom-0 w-64 bg-card border-r border-border z-30 flex flex-col">
      <div className="p-4 flex items-center justify-between">
        <h3 className="font-medium">Chapters</h3>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => setIsOpen(false)}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      <div className="p-4 pt-0">
        <Input placeholder="Search chapters..." className="bg-muted/50 text-sm h-8" />
      </div>

      <Separator />

      <div className="flex-1 overflow-y-auto p-3">
        {chapters.map((chapter) => (
          <div
            key={chapter.id}
            className={`rounded-md mb-1 transition-colors ${
              activeChapter === chapter.id ? "bg-accent/50 text-accent-foreground" : "hover:bg-muted"
            }`}
          >
            <div className="flex items-center p-2 cursor-pointer" onClick={() => setActiveChapter(chapter.id)}>
              <FileText className="h-4 w-4 mr-2 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium truncate">{chapter.title}</div>
                <div className="text-xs text-muted-foreground">{chapter.wordCount.toLocaleString()} words</div>
              </div>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0 opacity-0 group-hover:opacity-100">
                <MoreVertical className="h-3 w-3" />
              </Button>
            </div>
          </div>
        ))}
      </div>

      <div className="p-3 border-t border-border">
        <Button variant="outline" size="sm" className="w-full" onClick={addNewChapter}>
          <FilePlus className="h-4 w-4 mr-2" />
          Add New Chapter
        </Button>
      </div>
    </div>
  )
}
