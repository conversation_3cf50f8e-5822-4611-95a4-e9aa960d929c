"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Menu, X, PenTool, Crown } from "lucide-react"
import { useAuth } from "@/lib/providers/auth-provider"
import { UserDropdown } from "@/components/user-dropdown"

export function Navbar() {
  const [isOpen, setIsOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const pathname = usePathname()
  const { user } = useAuth()


  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setScrolled(true)
      } else {
        setScrolled(false)
      }
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled ? "bg-background/80 backdrop-blur-md border-b border-border/50" : "bg-transparent"
      }`}
    >
      <div className="container mx-auto px-4 md:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 md:h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <PenTool className="h-8 w-8 text-primary" />
            <span className="text-xl md:text-2xl font-bold">BookWriter</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {!user ? (
              <>
                <Link
                  href="/pricing"
                  className="px-4 py-2 text-sm text-foreground/80 hover:text-foreground transition-colors"
                >
                  Pricing
                </Link>
                <Link
                  href="/login"
                  className="px-4 py-2 text-sm text-foreground/80 hover:text-foreground transition-colors"
                >
                  Login
                </Link>
                <Link href="/signup" passHref>
                  <Button variant="gold" size="default">
                    Start Writing Free
                  </Button>
                </Link>
              </>
            ) : (
              <>
                {!pathname.includes("/pricing") ? (
                  <>
                    <Link
                      href="/dashboard"
                      className="px-4 py-2 text-sm text-foreground/80 hover:text-foreground transition-colors"
                    >
                      Dashboard
                    </Link>
                    <Link href="/pricing" passHref>
                      <Button variant="gold" size="sm" className="gap-1.5">
                        <Crown className="h-4 w-4" />
                        Credits
                      </Button>
                    </Link>
                  </>
                ) : (
                  <Link href="/dashboard" passHref>
                    <Button variant="gold" size="sm">
                      Dashboard
                    </Button>
                  </Link>
                )}
                <UserDropdown />
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button onClick={() => setIsOpen(!isOpen)} className="text-foreground hover:text-primary transition-colors">
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isOpen && (
        <div className="md:hidden bg-card/95 backdrop-blur-md border-b border-border">
          <div className="container mx-auto px-4 py-4 flex flex-col space-y-3">
            {!user ? (
              <>
                <Link
                  href="/pricing"
                  className="px-3 py-2 rounded-md hover:bg-muted transition-colors"
                  onClick={() => setIsOpen(false)}
                >
                  Pricing
                </Link>
                <Link
                  href="/login"
                  className="px-3 py-2 rounded-md hover:bg-muted transition-colors"
                  onClick={() => setIsOpen(false)}
                >
                  Login
                </Link>
                <Link href="/signup" onClick={() => setIsOpen(false)} passHref>
                  <Button variant="gold" className="w-full mt-2">
                    Start Writing Free
                  </Button>
                </Link>
              </>
            ) : (
              <>
                {!pathname.includes("/pricing") && (
                  <Link
                    href="/dashboard"
                    className="px-3 py-2 rounded-md hover:bg-muted transition-colors"
                    onClick={() => setIsOpen(false)}
                  >
                    Dashboard
                  </Link>
                )}
                <Link
                  href="/profile/settings"
                  className="px-3 py-2 rounded-md hover:bg-muted transition-colors"
                  onClick={() => setIsOpen(false)}
                >
                  Settings
                </Link>
                {!pathname.includes("/pricing") ? (
                  <Link href="/pricing" onClick={() => setIsOpen(false)} passHref>
                    <Button variant="gold" className="w-full flex items-center justify-center gap-1.5 mt-2">
                      <Crown className="h-4 w-4" />
                      Buy Magic Edit Credits
                    </Button>
                  </Link>
                ) : (
                  <Link href="/dashboard" onClick={() => setIsOpen(false)} passHref>
                    <Button variant="gold" className="w-full mt-2">
                      Dashboard
                    </Button>
                  </Link>
                )}
                <Link
                  href="/"
                  className="px-3 py-2 rounded-md hover:bg-muted transition-colors"
                  onClick={() => setIsOpen(false)}
                >
                  Logout
                </Link>
              </>
            )}
          </div>
        </div>
      )}
    </nav>
  )
}
