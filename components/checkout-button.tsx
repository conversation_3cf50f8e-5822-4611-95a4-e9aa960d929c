'use client';

import { useState } from 'react';
import { Button, ButtonProps } from '@/components/ui/button';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';
import { useAuth } from '@/lib/providers/auth-provider';

interface CheckoutButtonProps extends ButtonProps {
  planType?: 'monthly' | 'annual' | 'credit_single' | 'credit_5' | 'credit_25' | 'credit_100';
  className?: string;
  children?: React.ReactNode;
}

export function CheckoutButton({
  planType = 'monthly',
  className,
  children,
  ...props
}: CheckoutButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { user, getAuthToken } = useAuth();

  const handleCheckout = async () => {
    if (!user) {
      toast.error('Please sign in to upgrade your account');
      window.location.href = '/login?redirect=/pricing';
      return;
    }

    setIsLoading(true);

    try {
      // Get auth token for the API request
      const authToken = await getAuthToken();
      
      if (!authToken) {
        throw new Error('Authentication failed. Please try signing in again.');
      }

      // Prepare success and cancel URLs based on current location
      const successUrl = `${window.location.origin}/dashboard?checkout=success`;
      const cancelUrl = `${window.location.origin}/pricing?checkout=canceled`;

      // Call our checkout API
      const response = await fetch('/api/create-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
        },
        body: JSON.stringify({
          planType,
          successUrl,
          cancelUrl,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start checkout process');
      }

      // Get checkout URL from the response
      const { url } = await response.json();

      // Redirect to Stripe checkout
      if (url) {
        window.location.href = url;
      } else {
        throw new Error('No checkout URL returned');
      }
    } catch (error: any) {
      console.error('Checkout error:', error);
      toast.error(error.message || 'Failed to start checkout process');
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant="gold"
      className={className}
      onClick={handleCheckout}
      disabled={isLoading}
      {...props}
    >
      {isLoading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Preparing checkout...
        </>
      ) : (
        children || 'Upgrade Now'
      )}
    </Button>
  );
}