"use client"

import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"

import { cn } from "@/lib/utils"

const GoldProgress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>
>(({ className, value, ...props }, ref) => (
  <ProgressPrimitive.Root
    ref={ref}
    className={cn(
      "relative h-4 w-full overflow-hidden rounded-full bg-secondary",
      className
    )}
    {...props}
  >
    <ProgressPrimitive.Indicator
      className="h-full w-full flex-1 transition-all"
      style={{ 
        transform: `translateX(-${100 - (value || 0)}%)`,
        background: "linear-gradient(to right, #e6bc45, #d4af37, #e6bc45)",
        backgroundSize: "200% 100%",
        boxShadow: "0 0 6px rgba(212, 175, 55, 0.6)",
        animation: value && value > 0 ? "goldShimmer 1.5s infinite" : "none"
      }}
    />
  </ProgressPrimitive.Root>
))
GoldProgress.displayName = "GoldProgress"

export { GoldProgress }