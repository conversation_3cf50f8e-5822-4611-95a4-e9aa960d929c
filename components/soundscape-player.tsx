"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Volume2, VolumeX, ChevronDown, Coffee, CloudRain, Wind, Waves, Music } from "lucide-react"

const soundscapes = [
  { id: "cafe", name: "Café Ambience", icon: Coffee },
  { id: "rain", name: "Gentle Rain", icon: CloudRain },
  { id: "wind", name: "Forest Breeze", icon: Wind },
  { id: "waves", name: "Ocean Waves", icon: Waves },
  { id: "piano", name: "Soft Piano", icon: Music },
]

export function SoundscapePlayer() {
  const [isPlaying, setIsPlaying] = useState(false)
  const [activeSoundscape, setActiveSoundscape] = useState(soundscapes[0])
  const [volume, setVolume] = useState(70)

  const togglePlay = () => {
    setIsPlaying(!isPlaying)
  }

  return (
    <div className="fixed bottom-6 right-6 z-40">
      <div className="premium-card p-1 rounded-full">
        <div className="bg-card rounded-full flex items-center">
          <Button
            variant={isPlaying ? "secondary" : "ghost"}
            size="sm"
            className="h-10 w-10 rounded-full"
            onClick={togglePlay}
          >
            {isPlaying ? <Volume2 className="h-5 w-5 text-primary" /> : <VolumeX className="h-5 w-5" />}
          </Button>

          {isPlaying && (
            <div className="pr-2 flex items-center">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 text-xs flex items-center px-2 mr-2">
                    <activeSoundscape.icon className="h-3.5 w-3.5 mr-1" />
                    {activeSoundscape.name}
                    <ChevronDown className="h-3 w-3 ml-1 opacity-70" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Soundscapes</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {soundscapes.map((soundscape) => (
                    <DropdownMenuItem key={soundscape.id} onClick={() => setActiveSoundscape(soundscape)}>
                      <soundscape.icon className="h-4 w-4 mr-2" />
                      {soundscape.name}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              <div className="w-20 h-1.5 bg-muted rounded-full overflow-hidden">
                <div className="h-full bg-primary" style={{ width: `${volume}%` }}></div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
