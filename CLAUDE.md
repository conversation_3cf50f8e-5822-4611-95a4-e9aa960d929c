# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands
- **Dev**: `npm run dev` - Start the Next.js development server
- **Build**: `npm run build` - Build the production application
- **Start**: `npm run start` - Start the production server
- **Lint**: `npm run lint` - Run ESLint to check code quality
- **Type Check**: `tsc --noEmit` - Validate TypeScript types

## Architecture

### Project Overview
BookWriter is a modern writing platform built with Next.js that helps authors create, edit, and organize their books. Key features include:

- Rich text editing with TipTap
- Chapter organization and reordering
- AI-powered features like "Magic Edit" for improving text
- "BookMemory" for maintaining story consistency
- Export capabilities (PDF, DOCX, EPUB)
- Supabase authentication and data storage

### Core Components
1. **Authentication System** (`app/(auth)/`)
   - Login/signup pages using Supabase Auth
   - Protected routes with middleware

2. **Dashboard** (`app/dashboard/`)
   - Project listing and management
   - New project creation

3. **Writing Environment** (`app/project/[projectId]/`)
   - `EditorPanel`: Main text editor with TipTap
   - `ChapterSheet`: Chapter management sidebar
   - `MainToolbar`: Tools for saving, exporting, and more
   - `MagicEditDialog`: AI-powered editing interface

4. **API Routes** (`app/api/`)
   - `book-memory/`: AI-powered story summarization
   - `magic-edit/`: AI-powered text improvement
   - `user-credits/`: User subscription and credit management

### Key Data Flows
- User authentication → Project selection → Chapter editing → Export
- Chapter edits → Autosave to Supabase → BookMemory generation
- Text selection → Magic Edit → AI processing → Suggested edits

## Style Guidelines
- **Implementation Source**: Follow `/docs/RMD_BookWriter_v2.1.md` for implementation details
- **UI Components**: Use shadcn/ui components from components/ui/ directory
- **Imports**: Group by: 1) React/Next, 2) External libraries, 3) Internal components/utils
- **Components**: Use functional components with TypeScript interfaces for props
- **Naming**: PascalCase for components/interfaces, camelCase for functions/variables
- **CSS**: Use Tailwind with `cn()` utility from `@/lib/utils` for conditional classes
- **State Management**: Use React hooks (useState, useContext) for state
- **Error Handling**: Use toast.error for feedback, AlertDialog for critical errors
- **File Structure**: Follow Next.js App Router conventions (/app directory)
- **Code Formatting**: Maintain consistent indentation (2 spaces) and line breaks
- **Comments**: Minimal comments, focus on self-documenting code with clear naming

## Premium Features
- **Magic Edit**: AI-powered text improvement with credit-based usage limits
  - Free users: 5 edits per month
  - Paid users: 25 edits per month
  - Each edit processed through OpenAI API
  
- **BookMemory**: AI-powered chapter summarization for story consistency
  - Exclusive to paid users
  - Automatically triggered on chapter save
  - Accessible via BookMemory drawer

## Database Structure
- **users**: User authentication and profile info
- **projects**: Book projects with metadata
- **chapters**: Book chapters with content and ordering
- **magic_edits**: Tracks usage of Magic Edit feature
- **book_memory**: Stores AI-generated chapter summaries
- **subscriptions**: User subscription status

## Technology Stack
- Next.js 15+ (App Router)
- TypeScript (strict mode)
- Tailwind CSS
- shadcn/ui components (based on Radix UI)
- Supabase for authentication and backend
- TipTap for rich text editing
- react-hook-form with zod validation
- sonner for toast notifications
- OpenAI API for AI features